import { GoogleGenAI } from '@google/genai';
import { Message, IMessage } from '../models/Message';
import { IContact } from '../models/Contact';

// Função para limpar JSON retornado pelo Gemini (remove markdown)
function cleanGeminiJSON(text: string): string {
  try {
    console.log('🧹 Limpando JSON do Gemini:', text.substring(0, 100) + '...');

    // Remover markdown code blocks
    let cleaned = text.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Remover quebras de linha extras e espaços
    cleaned = cleaned.trim();

    // Se ainda não é um JSON válido, tentar extrair apenas o objeto JSON
    if (!cleaned.startsWith('{')) {
      const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleaned = jsonMatch[0];
      }
    }

    console.log('✅ JSON limpo:', cleaned.substring(0, 100) + '...');
    return cleaned;
  } catch (error) {
    console.error('❌ Erro ao limpar JSON do Gemini:', error);
    return text;
  }
}

// Função para validar e ajustar tamanho da resposta
function validateResponseLength(response: string, minWords: number = 15, maxWords: number = 60): string {
  const words = response.trim().split(/\s+/);
  const wordCount = words.length;

  console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);

  if (wordCount >= minWords && wordCount <= maxWords) {
    console.log('✅ Tamanho da resposta adequado');
    return response;
  }

  if (wordCount > maxWords) {
    // Truncar resposta se muito longa
    const truncated = words.slice(0, maxWords).join(' ');
    console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras`);
    return truncated + (truncated.endsWith('.') ? '' : '.');
  }

  if (wordCount < minWords) {
    console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
    return response;
  }

  return response;
}

export class GeminiAIService {
  private ai: GoogleGenAI | null = null;
  private isInitialized: boolean = false;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;

    if (!apiKey) {
      console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
      this.isInitialized = false;
      return;
    }

    try {
      this.ai = new GoogleGenAI({ apiKey });
      this.isInitialized = true;
      console.log('✅ Gemini AI inicializado com sucesso');
    } catch (error) {
      console.error('❌ Erro ao inicializar Gemini AI:', error);
      this.isInitialized = false;
    }
  }

  private async generateContent(prompt: string): Promise<string> {
    if (!this.isInitialized || !this.ai) {
      throw new Error('Gemini AI não está inicializado');
    }

    try {
      const response = await this.ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: prompt,
      });

      return response.text || '';
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      throw error;
    }
  }

  private async getConversationContext(contact: IContact, limit: number = 10) {
    const messages = await Message.find({ contact: contact._id })
      .sort({ timestamp: -1 })
      .limit(limit)
      .populate('contact');
    return messages.reverse();
  }

  private formatContext(messages: IMessage[]) {
    return messages.map(msg => ({
      role: msg.fromMe ? 'assistant' : 'user',
      content: msg.content
    }));
  }

  public async generateResponse(contact: IContact, message: string) {
    try {
      const context = await this.getConversationContext(contact);
      const formattedContext = this.formatContext(context);

      const prompt = `
        Você é "Rafaela", assistente especializada em gestação e cuidados maternos.
        Forneça suporte emocional e informações precisas para gestantes.

        Contexto da gestante:
        - Nome: ${contact.name}
        - Telefone: ${contact.phone}
        - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

        Histórico da conversa:
        ${formattedContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

        Nova mensagem da gestante: ${message}

        IMPORTANTE: Sua resposta deve ter ENTRE 15 E 60 PALAVRAS. Seja:
        1. Empática e acolhedora
        2. Use o nome da gestante quando apropriado
        3. Mencione o bebê usando o gênero correto se conhecido
        4. Precisa e baseada em evidências
        5. Prática quando relevante
        6. Profissional mas calorosa
        7. Sem julgamentos
        8. Sugira médico se necessário

        RESPOSTA CONCISA (15-60 palavras):`;

      const rawResponse = await this.generateContent(prompt);

      // Validar e ajustar tamanho da resposta (15-60 palavras)
      const response = validateResponseLength(rawResponse, 15, 60);

      // Análise de sentimento e necessidades
      const sentimentAnalysis = await this.analyzeSentiment(message, contact);

      return {
        response,
        sentiment: sentimentAnalysis.sentiment,
        needs: sentimentAnalysis.needs,
        suggestions: sentimentAnalysis.suggestions
      };
    } catch (error) {
      console.error('Erro ao gerar resposta:', error);
      throw error;
    }
  }

  private async analyzeSentiment(message: string, contact: IContact) {
    const prompt = `
      Analise a seguinte mensagem de uma gestante e forneça uma análise detalhada em formato JSON:
      
      Contexto:
      - Nome: ${contact.name}
      - Estágio da gestação: ${contact.pregnancyStage || 'Não informado'}
      
      Mensagem: ${message}
      
      Forneça uma análise com os seguintes campos:
      {
        "sentiment": {
          "type": "positive/negative/neutral",
          "score": 0.0 a 1.0,
          "emotions": ["lista de emoções identificadas"]
        },
        "needs": ["lista de necessidades identificadas"],
        "suggestions": ["sugestões de acompanhamento"],
        "priority": "alta/média/baixa",
        "medical_attention": true/false,
        "follow_up": ["ações de acompanhamento recomendadas"]
      }`;

    const result = await this.generateContent(prompt);

    try {
      // Limpar JSON antes de fazer parse
      const cleanedJSON = cleanGeminiJSON(result);
      return JSON.parse(cleanedJSON);
    } catch (parseError) {
      console.error('❌ Erro ao fazer parse do JSON:', parseError);
      console.error('📄 Resposta original do Gemini:', result);

      // Retornar análise padrão em caso de erro
      return {
        sentiment: {
          type: 'neutral',
          score: 0.5,
          emotions: ['indefinido']
        },
        needs: ['análise manual necessária'],
        suggestions: ['revisar mensagem manualmente'],
        priority: 'média',
        medical_attention: false,
        follow_up: ['acompanhar evolução']
      };
    }
  }

  public async generateFollowUpMessage(contact: IContact) {
    const prompt = `
      Gere uma mensagem de acompanhamento carinhosa para uma gestante:
      - Nome: ${contact.name}
      - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

      IMPORTANTE: Mensagem deve ter ENTRE 20 E 50 PALAVRAS. Deve:
      1. Usar o nome da gestante
      2. Mencionar o bebê com gênero correto se conhecido
      3. Perguntar sobre bem-estar
      4. Oferecer suporte
      5. Tom acolhedor e carinhoso
      6. Incluir dica geral de gestação

      MENSAGEM CONCISA (20-50 palavras):`;

    const rawResult = await this.generateContent(prompt);

    // Validar e ajustar tamanho da mensagem de follow-up (20-50 palavras)
    const result = validateResponseLength(rawResult, 20, 50);
    return result;
  }
} 
