"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Contact = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const ContactSchema = new mongoose_1.Schema({
    phone: {
        type: String,
        required: [true, 'Telefone é obrigatório'],
        unique: true,
        trim: true,
        match: [/^\+?[\d\s\-\(\)]+$/, 'Formato de telefone inválido']
    },
    name: {
        type: String,
        required: [true, 'Nome é obrigatório'],
        trim: true,
        minlength: [2, 'Nome deve ter pelo menos 2 caracteres'],
        maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email inválido']
    },
    pushname: {
        type: String,
        trim: true
    },
    dateOfBirth: {
        type: Date,
        validate: {
            validator: function (v) {
                return !v || v <= new Date();
            },
            message: 'Data de nascimento não pode ser no futuro'
        }
    },
    age: {
        type: Number,
        min: [10, 'Idade mínima é 10 anos'],
        max: [60, 'Idade máxima é 60 anos']
    },
    // Dados da gestação
    pregnancyStage: {
        type: String,
        enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
    },
    dueDate: {
        type: Date,
        validate: {
            validator: function (v) {
                return !v || v >= new Date();
            },
            message: 'Data prevista do parto deve ser no futuro'
        }
    },
    gestationalWeeks: {
        type: Number,
        min: [0, 'Semanas gestacionais não podem ser negativas'],
        max: [45, 'Semanas gestacionais não podem exceder 45']
    },
    isHighRisk: {
        type: Boolean,
        default: false
    },
    complications: [{
            type: String,
            trim: true
        }],
    // Dados médicos
    bloodType: {
        type: String,
        enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
    },
    allergies: [{
            type: String,
            trim: true
        }],
    medications: [{
            type: String,
            trim: true
        }],
    medicalHistory: {
        type: String,
        maxlength: [1000, 'Histórico médico deve ter no máximo 1000 caracteres']
    },
    // Dados de contato
    emergencyContact: {
        name: {
            type: String,
            trim: true
        },
        phone: {
            type: String,
            trim: true
        },
        relationship: {
            type: String,
            trim: true
        }
    },
    address: {
        street: {
            type: String,
            trim: true
        },
        city: {
            type: String,
            trim: true
        },
        state: {
            type: String,
            trim: true
        },
        zipCode: {
            type: String,
            trim: true
        }
    },
    // Dados de acompanhamento
    lastInteraction: {
        type: Date,
        default: Date.now
    },
    nextAppointment: {
        type: Date
    },
    notes: {
        type: String,
        maxlength: [2000, 'Notas devem ter no máximo 2000 caracteres']
    },
    tags: [{
            type: String,
            trim: true,
            lowercase: true
        }],
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    // Dados do sistema
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    },
    assignedTo: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});
// Índices para performance
ContactSchema.index({ phone: 1 });
ContactSchema.index({ name: 1 });
ContactSchema.index({ email: 1 });
ContactSchema.index({ pregnancyStage: 1 });
ContactSchema.index({ dueDate: 1 });
ContactSchema.index({ isHighRisk: 1 });
ContactSchema.index({ priority: 1 });
ContactSchema.index({ isActive: 1 });
ContactSchema.index({ createdBy: 1 });
ContactSchema.index({ assignedTo: 1 });
ContactSchema.index({ lastInteraction: -1 });
// Middleware para calcular idade automaticamente
ContactSchema.pre('save', function (next) {
    if (this.dateOfBirth && !this.age) {
        const today = new Date();
        const birthDate = new Date(this.dateOfBirth);
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        this.age = age;
    }
    next();
});
// Método para calcular idade gestacional
ContactSchema.methods.getGestationalAge = function () {
    if (!this.dueDate)
        return null;
    const today = new Date();
    const dueDate = new Date(this.dueDate);
    const gestationPeriod = 40 * 7; // 40 semanas em dias
    const daysSinceConception = gestationPeriod - Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return Math.max(0, Math.floor(daysSinceConception / 7));
};
// Método para obter status da gravidez
ContactSchema.methods.getPregnancyStatus = function () {
    const weeks = this.getGestationalAge();
    if (!weeks)
        return 'Não informado';
    if (weeks < 13)
        return 'Primeiro trimestre';
    if (weeks < 27)
        return 'Segundo trimestre';
    if (weeks < 40)
        return 'Terceiro trimestre';
    return 'Pós-termo';
};
// Método para verificar se está atrasada
ContactSchema.methods.isOverdue = function () {
    if (!this.dueDate)
        return false;
    return new Date() > new Date(this.dueDate);
};
exports.Contact = mongoose_1.default.model('Contact', ContactSchema);
