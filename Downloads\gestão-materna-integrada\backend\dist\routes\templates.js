"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const MessageTemplate_1 = require("../models/MessageTemplate");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validações
const templateValidation = [
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Nome deve ter entre 1 e 100 caracteres'),
    (0, express_validator_1.body)('category')
        .isIn(['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up'])
        .withMessage('Categoria inválida'),
    (0, express_validator_1.body)('title')
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Título deve ter entre 1 e 200 caracteres'),
    (0, express_validator_1.body)('content')
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage('Conteúdo deve ter entre 1 e 2000 caracteres')
];
// GET /api/templates - Listar templates
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('category').optional().isIn(['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up']),
    (0, express_validator_1.query)('isActive').optional().isBoolean(),
    (0, express_validator_1.query)('search').optional().isLength({ max: 100 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const category = req.query.category;
        const isActive = req.query.isActive === 'true';
        const search = req.query.search;
        // Construir filtros
        const filters = {};
        if (category)
            filters.category = category;
        if (req.query.isActive !== undefined)
            filters.isActive = isActive;
        if (search) {
            filters.$or = [
                { name: { $regex: search, $options: 'i' } },
                { title: { $regex: search, $options: 'i' } },
                { content: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (page - 1) * limit;
        const [templates, total] = await Promise.all([
            MessageTemplate_1.MessageTemplate.find(filters)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .populate('createdBy', 'name email')
                .populate('lastModifiedBy', 'name email')
                .lean(),
            MessageTemplate_1.MessageTemplate.countDocuments(filters)
        ]);
        res.json({
            templates,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            filters: {
                category,
                isActive: req.query.isActive !== undefined ? isActive : undefined,
                search
            }
        });
    }
    catch (error) {
        console.error('Erro ao listar templates:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/templates/:id - Obter template específico
router.get('/:id', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), async (req, res) => {
    try {
        const template = await MessageTemplate_1.MessageTemplate.findById(req.params.id)
            .populate('createdBy', 'name email')
            .populate('lastModifiedBy', 'name email');
        if (!template) {
            return res.status(404).json({
                error: 'Template não encontrado',
                code: 'TEMPLATE_NOT_FOUND'
            });
        }
        res.json({ template });
    }
    catch (error) {
        console.error('Erro ao obter template:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/templates - Criar template
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), templateValidation, (0, auth_1.auditLog)('CREATE_TEMPLATE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        // Verificar se nome já existe
        const existingTemplate = await MessageTemplate_1.MessageTemplate.findOne({ name: req.body.name });
        if (existingTemplate) {
            return res.status(409).json({
                error: 'Nome do template já existe',
                code: 'TEMPLATE_NAME_EXISTS'
            });
        }
        // Extrair variáveis do conteúdo
        const variables = extractVariables(req.body.content);
        // Criar template
        const templateData = {
            ...req.body,
            variables,
            createdBy: req.user._id,
            usageCount: 0,
            successRate: 0
        };
        const template = new MessageTemplate_1.MessageTemplate(templateData);
        await template.save();
        await template.populate('createdBy', 'name email');
        res.status(201).json({
            message: 'Template criado com sucesso',
            template
        });
    }
    catch (error) {
        console.error('Erro ao criar template:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// PUT /api/templates/:id - Atualizar template
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), templateValidation, (0, auth_1.auditLog)('UPDATE_TEMPLATE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const template = await MessageTemplate_1.MessageTemplate.findById(req.params.id);
        if (!template) {
            return res.status(404).json({
                error: 'Template não encontrado',
                code: 'TEMPLATE_NOT_FOUND'
            });
        }
        // Verificar se nome já existe (exceto o atual)
        if (req.body.name !== template.name) {
            const existingTemplate = await MessageTemplate_1.MessageTemplate.findOne({
                name: req.body.name,
                _id: { $ne: template._id }
            });
            if (existingTemplate) {
                return res.status(409).json({
                    error: 'Nome do template já existe',
                    code: 'TEMPLATE_NAME_EXISTS'
                });
            }
        }
        // Extrair variáveis do conteúdo
        const variables = extractVariables(req.body.content);
        // Atualizar template
        Object.assign(template, req.body);
        template.variables = variables;
        template.lastModifiedBy = req.user._id;
        await template.save();
        await template.populate('createdBy', 'name email');
        await template.populate('lastModifiedBy', 'name email');
        res.json({
            message: 'Template atualizado com sucesso',
            template
        });
    }
    catch (error) {
        console.error('Erro ao atualizar template:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// DELETE /api/templates/:id - Desativar template
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), (0, auth_1.auditLog)('DEACTIVATE_TEMPLATE'), async (req, res) => {
    try {
        const template = await MessageTemplate_1.MessageTemplate.findById(req.params.id);
        if (!template) {
            return res.status(404).json({
                error: 'Template não encontrado',
                code: 'TEMPLATE_NOT_FOUND'
            });
        }
        // Desativar template (não deletar para manter histórico)
        template.isActive = false;
        template.lastModifiedBy = req.user._id;
        await template.save();
        res.json({
            message: 'Template desativado com sucesso'
        });
    }
    catch (error) {
        console.error('Erro ao desativar template:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/templates/:id/preview - Visualizar template renderizado
router.post('/:id/preview', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), [
    (0, express_validator_1.body)('variables')
        .optional()
        .isObject()
        .withMessage('Variáveis devem ser um objeto')
], async (req, res) => {
    try {
        const template = await MessageTemplate_1.MessageTemplate.findById(req.params.id);
        if (!template) {
            return res.status(404).json({
                error: 'Template não encontrado',
                code: 'TEMPLATE_NOT_FOUND'
            });
        }
        const variables = new Map(Object.entries(req.body.variables || {}).map(([k, v]) => [k, String(v)]));
        // Adicionar variáveis padrão para preview
        if (!variables.has('name'))
            variables.set('name', 'Maria');
        if (!variables.has('week'))
            variables.set('week', '24');
        if (!variables.has('stage'))
            variables.set('stage', 'segundo trimestre');
        const renderedContent = template.render(variables);
        res.json({
            original: template.content,
            rendered: renderedContent,
            variables: Array.from(variables.entries()).reduce((obj, [key, value]) => {
                obj[key] = value;
                return obj;
            }, {}),
            availableVariables: template.variables
        });
    }
    catch (error) {
        console.error('Erro ao visualizar template:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/templates/stats/usage - Estatísticas de uso dos templates
router.get('/stats/usage', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const stats = await MessageTemplate_1.MessageTemplate.aggregate([
            { $match: { isActive: true } },
            {
                $group: {
                    _id: '$category',
                    totalTemplates: { $sum: 1 },
                    totalUsage: { $sum: '$usageCount' },
                    avgSuccessRate: { $avg: '$successRate' }
                }
            },
            { $sort: { totalUsage: -1 } }
        ]);
        const topTemplates = await MessageTemplate_1.MessageTemplate.find({ isActive: true })
            .sort({ usageCount: -1 })
            .limit(10)
            .select('name category usageCount successRate')
            .lean();
        res.json({
            byCategory: stats,
            topTemplates
        });
    }
    catch (error) {
        console.error('Erro ao obter estatísticas de templates:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// Função auxiliar para extrair variáveis do conteúdo
function extractVariables(content) {
    const regex = /\{\{(\w+)\}\}/g;
    const variables = [];
    let match;
    while ((match = regex.exec(content)) !== null) {
        if (!variables.includes(match[1])) {
            variables.push(match[1]);
        }
    }
    return variables;
}
exports.default = router;
