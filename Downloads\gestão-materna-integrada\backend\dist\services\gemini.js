"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiAIService = void 0;
const generative_ai_1 = require("@google/generative-ai");
const Message_1 = require("../models/Message");
const genAI = new generative_ai_1.GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');
class GeminiAIService {
    constructor() {
        this.model = genAI.getGenerativeModel({ model: 'gemini-1.0-pro' });
    }
    async getConversationContext(contact, limit = 10) {
        const messages = await Message_1.Message.find({ contact: contact._id })
            .sort({ timestamp: -1 })
            .limit(limit)
            .populate('contact');
        return messages.reverse();
    }
    formatContext(messages) {
        return messages.map(msg => ({
            role: msg.fromMe ? 'assistant' : 'user',
            content: msg.content
        }));
    }
    async generateResponse(contact, message) {
        try {
            const context = await this.getConversationContext(contact);
            const formattedContext = this.formatContext(context);
            const prompt = `
        Você é um assistente especializado em gestação e cuidados maternos, chamado "Assistente Materno".
        Seu objetivo é fornecer suporte emocional e informações precisas para gestantes.

        Contexto da gestante:
        - Nome: ${contact.name}
        - Estágio da gestação: ${contact.pregnancyStage || 'Não informado'}
        - Observações: ${contact.notes || 'Não informado'}

        Histórico da conversa:
        ${formattedContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}
        
        Nova mensagem da gestante: ${message}
        
        Por favor, forneça uma resposta que:
        1. Seja empática e acolhedora
        2. Considere o estágio específico da gestação
        3. Forneça informações precisas e baseadas em evidências
        4. Inclua dicas práticas quando relevante
        5. Mantenha um tom profissional mas caloroso
        6. Evite julgamentos ou críticas
        7. Sugira consulta médica quando necessário
        
        Resposta:`;
            const result = await this.model.generateContent(prompt);
            const response = result.response.text();
            // Análise de sentimento e necessidades
            const sentimentAnalysis = await this.analyzeSentiment(message, contact);
            return {
                response,
                sentiment: sentimentAnalysis.sentiment,
                needs: sentimentAnalysis.needs,
                suggestions: sentimentAnalysis.suggestions
            };
        }
        catch (error) {
            console.error('Erro ao gerar resposta:', error);
            throw error;
        }
    }
    async analyzeSentiment(message, contact) {
        const prompt = `
      Analise a seguinte mensagem de uma gestante e forneça uma análise detalhada em formato JSON:
      
      Contexto:
      - Nome: ${contact.name}
      - Estágio da gestação: ${contact.pregnancyStage || 'Não informado'}
      
      Mensagem: ${message}
      
      Forneça uma análise com os seguintes campos:
      {
        "sentiment": {
          "type": "positive/negative/neutral",
          "score": 0.0 a 1.0,
          "emotions": ["lista de emoções identificadas"]
        },
        "needs": ["lista de necessidades identificadas"],
        "suggestions": ["sugestões de acompanhamento"],
        "priority": "alta/média/baixa",
        "medical_attention": true/false,
        "follow_up": ["ações de acompanhamento recomendadas"]
      }`;
        const result = await this.model.generateContent(prompt);
        return JSON.parse(result.response.text());
    }
    async generateFollowUpMessage(contact) {
        const prompt = `
      Gere uma mensagem de acompanhamento para uma gestante com as seguintes características:
      - Nome: ${contact.name}
      - Estágio da gestação: ${contact.pregnancyStage || 'Não informado'}
      - Observações: ${contact.notes || 'Não informado'}
      
      A mensagem deve:
      1. Ser personalizada para o estágio da gestação
      2. Incluir dicas relevantes
      3. Perguntar sobre o bem-estar
      4. Oferecer suporte
      5. Manter um tom acolhedor
      
      Mensagem:`;
        const result = await this.model.generateContent(prompt);
        return result.response.text();
    }
}
exports.GeminiAIService = GeminiAIService;
