
import React from 'react';
import PageTitle from '../shared/PageTitle';
import MetricCard from './MetricCard';
import PregnantWomenChart from './PregnantWomenChart';
import MessageStatsChart from './MessageStatsChart';
import Spinner from '../shared/Spinner';
import { useDashboard, useDashboardFreshness } from '../../src/hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const DashboardPage: React.FC = () => {
  const { data, loading, error, refetch, lastUpdated } = useDashboard();
  const isStale = useDashboardFreshness();
  const { metrics, newRegistrations, ageDistribution, recentActivities, topContacts } = data;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <div className="text-center">
        <div className="text-red-500 mb-4">
          {error || "Erro ao carregar os dados do dashboard."}
        </div>
        <button
          onClick={refetch}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Dashboard Visão Geral" subtitle="Métricas e estatísticas chave do sistema." />

        <div className="flex items-center gap-4">
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Atualizado {formatDistanceToNow(lastUpdated, {
                addSuffix: true,
                locale: ptBR
              })}
            </span>
          )}

          {isStale && (
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
              Dados desatualizados
            </span>
          )}

          <button
            onClick={refetch}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            disabled={loading}
          >
            {loading ? 'Atualizando...' : 'Atualizar'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <MetricCard title="Total de Gestantes" value={metrics.totalPregnantWomen.toString()} change="+5 esta semana" changeType="positive" />
        <MetricCard title="Média Semanas Gestação" value={`${metrics.avgWeeksGestation} sem.`} />
        <MetricCard title="Mensagens Enviadas (Mês)" value={metrics.messagesSentThisMonth.toString()} />
        <MetricCard title="Conversas Ativas" value={metrics.activeConversations.toString()} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Novas Gestantes Cadastradas (Últimos 6 Meses)</h3>
          {newRegistrations.length > 0 ? <PregnantWomenChart data={newRegistrations} /> : <p>Sem dados para exibir.</p>}
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Distribuição de Idade das Gestantes</h3>
          {ageDistribution.length > 0 ? <MessageStatsChart data={ageDistribution} /> : <p>Sem dados para exibir.</p>}
        </div>
      </div>
      
      {/* Seções de Atividades Recentes e Top Contatos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Contatos Mais Ativos</h3>
          {topContacts && topContacts.length > 0 ? (
            <ul className="space-y-3">
              {topContacts.map((contact: any, index: number) => (
                <li key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium text-gray-900">{contact.name}</div>
                    <div className="text-sm text-gray-500">{contact.phone}</div>
                    <div className="text-xs text-blue-600 capitalize">{contact.pregnancyStage?.replace('_', ' ')}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-primary">{contact.messageCount}</div>
                    <div className="text-xs text-gray-500">mensagens</div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">Nenhuma atividade recente encontrada.</p>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Resumo do Sistema</h3>
          <ul className="space-y-3 text-sm">
            <li className="flex justify-between items-center p-2 bg-green-50 rounded">
              <span className="text-green-700">Status WhatsApp:</span>
              <span className="font-medium text-green-800">✅ Conectado</span>
            </li>
            <li className="flex justify-between items-center p-2 bg-blue-50 rounded">
              <span className="text-blue-700">Total de Gestantes:</span>
              <span className="font-medium text-blue-800">{metrics?.totalPregnantWomen || 0}</span>
            </li>
            <li className="flex justify-between items-center p-2 bg-purple-50 rounded">
              <span className="text-purple-700">Mensagens do Mês:</span>
              <span className="font-medium text-purple-800">{metrics?.messagesSentThisMonth || 0}</span>
            </li>
            <li className="flex justify-between items-center p-2 bg-orange-50 rounded">
              <span className="text-orange-700">Conversas Ativas:</span>
              <span className="font-medium text-orange-800">{metrics?.activeConversations || 0}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
    