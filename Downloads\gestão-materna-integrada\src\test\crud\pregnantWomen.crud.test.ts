import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import PregnantListPage from '../../../components/pregnant/PregnantListPage';
import PregnantFormModal from '../../../components/pregnant/PregnantFormModal';
import DeleteConfirmModal from '../../../components/pregnant/DeleteConfirmModal';
import { apiService } from '../../services/apiService';

// Mock do apiService
vi.mock('../../services/apiService', () => ({
  apiService: {
    getPregnantWomen: vi.fn(),
    createPregnantWoman: vi.fn(),
    updatePregnantWoman: vi.fn(),
    deletePregnantWoman: vi.fn(),
  },
}));

// Mock do toast
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
  ToastContainer: () => <div data-testid="toast-container" />,
}));

// Mock dos dados de teste
const mockPregnantWomen = [
  {
    id: '1',
    name: 'Ana Silva',
    dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(), // 6 meses no futuro
    phone: '11999999999',
    email: '<EMAIL>',
    observations: 'Primeira gestação',
    createdAt: '2024-01-01T00:00:00.000Z',
    age: 28,
  },
  {
    id: '2',
    name: 'Maria Santos',
    dueDate: new Date(Date.now() + 8 * 30 * 24 * 60 * 60 * 1000).toISOString(), // 8 meses no futuro
    phone: '11888888888',
    email: '<EMAIL>',
    observations: 'Segunda gestação',
    createdAt: '2024-01-02T00:00:00.000Z',
    age: 32,
  },
];

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
      <ToastContainer />
    </BrowserRouter>
  );
};

describe('CRUD de Gestantes', () => {
  const mockApiService = vi.mocked(apiService);

  beforeEach(() => {
    vi.clearAllMocks();
    mockApiService.getPregnantWomen.mockResolvedValue(mockPregnantWomen);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('READ - Listar Gestantes', () => {
    it('deve carregar e exibir lista de gestantes', async () => {
      renderWithRouter(<PregnantListPage />);

      // Verificar se está carregando
      expect(screen.getByText(/carregando/i)).toBeInTheDocument();

      // Aguardar carregamento
      await waitFor(() => {
        expect(screen.getByText('Ana Silva')).toBeInTheDocument();
        expect(screen.getByText('Maria Santos')).toBeInTheDocument();
      });

      // Verificar se a API foi chamada
      expect(mockApiService.getPregnantWomen).toHaveBeenCalledTimes(1);
    });

    it('deve exibir mensagem quando não há gestantes', async () => {
      mockApiService.getPregnantWomen.mockResolvedValue([]);

      renderWithRouter(<PregnantListPage />);

      await waitFor(() => {
        expect(screen.getByText('Nenhuma gestante encontrada.')).toBeInTheDocument();
      });
    });

    it('deve filtrar gestantes por busca', async () => {
      renderWithRouter(<PregnantListPage />);

      await waitFor(() => {
        expect(screen.getByText('Ana Silva')).toBeInTheDocument();
        expect(screen.getByText('Maria Santos')).toBeInTheDocument();
      });

      // Buscar por nome
      const searchInput = screen.getByPlaceholderText(/buscar por nome/i);
      await userEvent.type(searchInput, 'Ana');

      await waitFor(() => {
        expect(screen.getByText('Ana Silva')).toBeInTheDocument();
        expect(screen.queryByText('Maria Santos')).not.toBeInTheDocument();
      });
    });

    it('deve exibir erro quando falha ao carregar gestantes', async () => {
      mockApiService.getPregnantWomen.mockRejectedValue(new Error('Erro de rede'));

      renderWithRouter(<PregnantListPage />);

      await waitFor(() => {
        expect(screen.getByText(/erro ao carregar/i)).toBeInTheDocument();
      });
    });
  });

  describe('CREATE - Criar Gestante', () => {
    it('deve abrir modal de criação ao clicar em Nova Gestante', async () => {
      renderWithRouter(<PregnantListPage />);

      await waitFor(() => {
        expect(screen.getByText('Ana Silva')).toBeInTheDocument();
      });

      const newButton = screen.getByText(/nova gestante/i);
      await userEvent.click(newButton);

      expect(screen.getByText(/cadastrar gestante/i)).toBeInTheDocument();
    });

    it('deve criar nova gestante com sucesso', async () => {
      const newPregnant = {
        id: '3',
        name: 'Carla Oliveira',
        dueDate: new Date(Date.now() + 7 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11777777777',
        email: '<EMAIL>',
        observations: 'Primeira gestação',
        createdAt: new Date().toISOString(),
        age: 25,
      };

      mockApiService.createPregnantWoman.mockResolvedValue(newPregnant);

      const mockOnSave = vi.fn().mockResolvedValue(undefined);

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      // Preencher formulário
      await userEvent.type(screen.getByLabelText(/nome/i), 'Carla Oliveira');
      await userEvent.type(screen.getByLabelText(/telefone/i), '11777777777');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');

      // Submeter formulário
      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Carla Oliveira',
          phone: '11777777777',
          email: '<EMAIL>',
        })
      );
    });

    it('deve validar campos obrigatórios ao criar gestante', async () => {
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      // Tentar submeter sem preencher campos
      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      // Verificar se não chamou onSave (validação do HTML5)
      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('deve exibir erro quando falha ao criar gestante', async () => {
      mockApiService.createPregnantWoman.mockRejectedValue(new Error('Erro ao criar'));

      const mockOnSave = vi.fn().mockRejectedValue(new Error('Erro ao criar'));

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      // Preencher e submeter
      await userEvent.type(screen.getByLabelText(/nome/i), 'Teste');
      await userEvent.type(screen.getByLabelText(/telefone/i), '11999999999');

      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      expect(mockOnSave).toHaveBeenCalled();
    });
  });

  describe('UPDATE - Atualizar Gestante', () => {
    it('deve abrir modal de edição com dados preenchidos', async () => {
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={mockPregnantWomen[0]}
        />
      );

      // Verificar se campos estão preenchidos
      expect(screen.getByDisplayValue('Ana Silva')).toBeInTheDocument();
      expect(screen.getByDisplayValue('11999999999')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });

    it('deve atualizar gestante com sucesso', async () => {
      const updatedPregnant = {
        ...mockPregnantWomen[0],
        name: 'Ana Silva Santos',
        phone: '11999999998',
      };

      mockApiService.updatePregnantWoman.mockResolvedValue(updatedPregnant);

      const mockOnSave = vi.fn().mockResolvedValue(undefined);

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={mockPregnantWomen[0]}
        />
      );

      // Alterar nome
      const nameInput = screen.getByDisplayValue('Ana Silva');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'Ana Silva Santos');

      // Submeter
      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Ana Silva Santos',
        })
      );
    });

    it('deve cancelar edição sem salvar alterações', async () => {
      const mockOnClose = vi.fn();
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          contact={mockPregnantWomen[0]}
        />
      );

      // Alterar campo
      const nameInput = screen.getByDisplayValue('Ana Silva');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'Nome Alterado');

      // Cancelar
      const cancelButton = screen.getByText(/cancelar/i);
      await userEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalled();
      expect(mockOnSave).not.toHaveBeenCalled();
    });
  });

  describe('DELETE - Excluir Gestante', () => {
    it('deve abrir modal de confirmação de exclusão', async () => {
      renderWithRouter(
        <DeleteConfirmModal
          isOpen={true}
          onClose={vi.fn()}
          onConfirm={vi.fn()}
          contactName="Ana Silva"
        />
      );

      expect(screen.getByText(/confirmar exclusão/i)).toBeInTheDocument();
      expect(screen.getByText(/ana silva/i)).toBeInTheDocument();
      expect(screen.getByText(/esta ação não pode ser desfeita/i)).toBeInTheDocument();
    });

    it('deve excluir gestante com sucesso', async () => {
      mockApiService.deletePregnantWoman.mockResolvedValue();

      const mockOnConfirm = vi.fn().mockResolvedValue(undefined);
      const mockOnClose = vi.fn();

      renderWithRouter(
        <DeleteConfirmModal
          isOpen={true}
          onClose={mockOnClose}
          onConfirm={mockOnConfirm}
          contactName="Ana Silva"
        />
      );

      const confirmButton = screen.getByText(/excluir/i);
      await userEvent.click(confirmButton);

      expect(mockOnConfirm).toHaveBeenCalled();
    });

    it('deve cancelar exclusão', async () => {
      const mockOnConfirm = vi.fn();
      const mockOnClose = vi.fn();

      renderWithRouter(
        <DeleteConfirmModal
          isOpen={true}
          onClose={mockOnClose}
          onConfirm={mockOnConfirm}
          contactName="Ana Silva"
        />
      );

      const cancelButton = screen.getByText(/cancelar/i);
      await userEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalled();
      expect(mockOnConfirm).not.toHaveBeenCalled();
    });

    it('deve exibir erro quando falha ao excluir gestante', async () => {
      mockApiService.deletePregnantWoman.mockRejectedValue(new Error('Erro ao excluir'));

      const mockOnConfirm = vi.fn().mockRejectedValue(new Error('Erro ao excluir'));

      renderWithRouter(
        <DeleteConfirmModal
          isOpen={true}
          onClose={vi.fn()}
          onConfirm={mockOnConfirm}
          contactName="Ana Silva"
        />
      );

      const confirmButton = screen.getByText(/excluir/i);
      await userEvent.click(confirmButton);

      expect(mockOnConfirm).toHaveBeenCalled();
    });
  });
});
