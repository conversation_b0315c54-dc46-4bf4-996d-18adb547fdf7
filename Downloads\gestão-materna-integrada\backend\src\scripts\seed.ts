import dotenv from 'dotenv';
import { connectDatabase } from '../config/database';
import { Contact } from '../models/Contact';
import { Message } from '../models/Message';
import { User } from '../models/User';

// Carregar variáveis de ambiente
dotenv.config();

const seedDatabase = async () => {
  try {
    await connectDatabase();

    console.log('🌱 Iniciando seed do banco de dados...');

    // Limpar coleções existentes
    await User.deleteMany({});
    await Contact.deleteMany({});
    await Message.deleteMany({});

    console.log('🗑️  Dados existentes removidos');

    // Criar usuário administrador
    const adminUser = await User.create({
      name: 'Administrador',
      email: '<EMAIL>',
      password: 'Admin123!@#',
      role: 'admin'
    });

    console.log('👤 Usuário administrador criado');
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Senha: Admin123!@#`);

    // Criar usuários de exemplo
    const users = await User.create([
      {
        name: 'Dr. <PERSON> <PERSON>',
        email: '<EMAIL>',
        password: 'Doctor123!',
        role: 'doctor'
      },
      {
        name: 'Enfermeira Maria',
        email: '<EMAIL>',
        password: 'Nurse123!',
        role: 'nurse'
      },
      {
        name: 'Coordenadora Ana',
        email: '<EMAIL>',
        password: 'Coord123!',
        role: 'coordinator'
      }
    ]);

    console.log(`👥 ${users.length} usuários de exemplo criados`);

    // Criar contatos de exemplo
    const contacts = await Contact.create([
      {
        phone: '+5511999999999',
        name: 'Ana Silva',
        email: '<EMAIL>',
        dateOfBirth: new Date('1995-03-15'),
        pregnancyStage: 'second_trimester',
        dueDate: new Date(Date.now() + 4 * 30 * 24 * 60 * 60 * 1000), // 4 meses no futuro
        gestationalWeeks: 24,
        isHighRisk: false,
        bloodType: 'O+',
        priority: 'medium',
        notes: 'Primeira gestação, sem complicações aparentes',
        tags: ['primeira-gestacao', 'saudavel'],
        createdBy: adminUser._id,
        assignedTo: users[0]._id, // Dr. João
        emergencyContact: {
          name: 'Carlos Silva',
          phone: '+5511888888888',
          relationship: 'Esposo'
        },
        address: {
          street: 'Rua das Flores, 123',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01234-567'
        }
      },
      {
        phone: '+5511888888888',
        name: 'Maria Santos',
        email: '<EMAIL>',
        dateOfBirth: new Date('1988-07-22'),
        pregnancyStage: 'third_trimester',
        dueDate: new Date(Date.now() + 2 * 30 * 24 * 60 * 60 * 1000), // 2 meses no futuro
        gestationalWeeks: 32,
        isHighRisk: true,
        bloodType: 'A+',
        priority: 'high',
        complications: ['diabetes gestacional', 'hipertensão'],
        medications: ['Insulina', 'Metformina'],
        notes: 'Segunda gestação, diabetes gestacional controlada',
        tags: ['diabetes', 'alto-risco', 'segunda-gestacao'],
        createdBy: adminUser._id,
        assignedTo: users[0]._id, // Dr. João
        emergencyContact: {
          name: 'Pedro Santos',
          phone: '+5511777777777',
          relationship: 'Esposo'
        }
      }
    ]);

    console.log(`🤱 ${contacts.length} contatos (gestantes) criados`);

    // Criar algumas mensagens de exemplo
    const messages = await Message.create([
      {
        contact: contacts[0]._id,
        content: 'Olá! Como você está se sentindo hoje?',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
        type: 'text',
        fromMe: true
      },
      {
        contact: contacts[0]._id,
        content: 'Estou bem, obrigada! Apenas um pouco de enjoo pela manhã.',
        timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 horas atrás
        type: 'text',
        fromMe: false,
        sentiment: {
          type: 'neutral',
          score: 0.6
        },
        needs: ['orientação sobre enjoos'],
        suggestions: ['dicas para aliviar enjoos matinais']
      },
      {
        contact: contacts[1]._id,
        content: 'Lembre-se de verificar sua glicemia hoje.',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 dia atrás
        type: 'text',
        fromMe: true
      },
      {
        contact: contacts[1]._id,
        content: 'Já verifiquei! Está 95 mg/dL em jejum.',
        timestamp: new Date(Date.now() - 23 * 60 * 60 * 1000), // 23 horas atrás
        type: 'text',
        fromMe: false,
        sentiment: {
          type: 'positive',
          score: 0.8
        },
        needs: ['acompanhamento glicêmico'],
        suggestions: ['manter controle regular']
      }
    ]);

    console.log(`💬 ${messages.length} mensagens de exemplo criadas`);

    console.log('\n✅ Seed executado com sucesso!');
    console.log('\n📋 Resumo:');
    console.log(`   👤 1 administrador + ${users.length} usuários`);
    console.log(`   🤱 ${contacts.length} gestantes`);
    console.log(`   💬 ${messages.length} mensagens`);

    console.log('\n🔐 Credenciais de acesso:');
    console.log('   Admin: <EMAIL> / Admin123!@#');
    console.log('   Doutor: <EMAIL> / Doctor123!');
    console.log('   Enfermeira: <EMAIL> / Nurse123!');
    console.log('   Coordenadora: <EMAIL> / Coord123!');

    process.exit(0);
  } catch (error) {
    console.error('❌ Erro ao executar seed:', error);
    process.exit(1);
  }
};

seedDatabase();