
import React, { useState, useEffect, useCallback } from 'react';
import PageTitle from '../shared/PageTitle';
import WhatsAppConnectCard from './WhatsAppConnectCard';
import ConversationView from './ConversationView';
import { PregnantWoman, Message, WhatsAppConnectionStatus } from '../../types';
import { MOCK_PREGNANT_WOMEN, MOCK_MESSAGES } from '../../constants';
import Select from './Select'; // A new Select component
import Spinner from '../shared/Spinner';

// Mock service for fetching pregnant women
const fetchPregnantWomenNames = async (): Promise<{ id: string, name: string }[]> => {
  return new Promise(resolve => 
    setTimeout(() => 
      resolve(MOCK_PREGNANT_WOMEN.map(p => ({ id: p.id, name: p.name }))), 
    500)
  );
};

const WhatsAppPage: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<WhatsAppConnectionStatus>(WhatsAppConnectionStatus.DISCONNECTED);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [pregnantWomenOptions, setPregnantWomenOptions] = useState<{ value: string; label: string }[]>([]);
  const [selectedPregnantWoman, setSelectedPregnantWoman] = useState<PregnantWoman | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loadingContacts, setLoadingContacts] = useState(true);

  useEffect(() => {
    const loadContacts = async () => {
      setLoadingContacts(true);
      const women = await fetchPregnantWomenNames();
      setPregnantWomenOptions(women.map(w => ({ value: w.id, label: w.name })));
      setLoadingContacts(false);
    };
    loadContacts();
  }, []);

  // Simulate WhatsApp connection logic
  const handleConnect = useCallback(() => {
    setConnectionStatus(WhatsAppConnectionStatus.CONNECTING);
    // Simulate QR code generation
    setTimeout(() => {
      setQrCodeUrl('https://picsum.photos/seed/qr/250/250'); // Placeholder QR
      setConnectionStatus(WhatsAppConnectionStatus.QR_REQUIRED);
    }, 1500);

    // Simulate connection success after a while
    setTimeout(() => {
      if (connectionStatus === WhatsAppConnectionStatus.QR_REQUIRED) { // Only if QR was shown
        setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
        setQrCodeUrl(null);
      }
    }, 10000); // User "scans" QR
  }, [connectionStatus]);

  const handleDisconnect = () => {
    setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
    setQrCodeUrl(null);
  };
  
  const handleSelectPregnantWoman = (id: string) => {
    const woman = MOCK_PREGNANT_WOMEN.find(p => p.id === id);
    if (woman) {
      setSelectedPregnantWoman(woman);
      // Load mock messages for this woman
      setMessages([...MOCK_MESSAGES.map(m => ({...m, id: m.id + Date.now() * Math.random() }))]); // Make IDs unique for demo
    } else {
      setSelectedPregnantWoman(null);
      setMessages([]);
    }
  };

  const handleSendMessage = (text: string): Message => {
    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      sender: 'user',
      text,
      timestamp: new Date().toISOString(),
      status: 'sent',
    };
    setMessages(prev => [...prev, newMessage]);
    // Simulate reply after a short delay
    setTimeout(() => {
        const replyMessage: Message = {
            id: `msg_reply_${Date.now()}`,
            sender: 'gestante',
            text: "Ok, recebido!",
            timestamp: new Date().toISOString(),
            status: 'delivered'
        };
        setMessages(prev => [...prev, replyMessage]);
    }, 1500);
    return newMessage;
  };

  const handleSendAiMessage = (text: string): Message => {
     const aiMessage: Message = {
      id: `msg_ai_${Date.now()}`,
      sender: 'ai', // Sent by AI on behalf of user
      text: `(Sugestão IA) ${text}`,
      timestamp: new Date().toISOString(),
      status: 'sent',
    };
    setMessages(prev => [...prev, aiMessage]);
    // Simulate reply
     setTimeout(() => {
        const replyMessage: Message = {
            id: `msg_reply_ai_${Date.now()}`,
            sender: 'gestante',
            text: "Entendido, obrigada pela sugestão!",
            timestamp: new Date().toISOString(),
            status: 'delivered'
        };
        setMessages(prev => [...prev, replyMessage]);
    }, 1500);
    return aiMessage;
  }


  return (
    <div>
      <PageTitle title="WhatsApp & Inteligência Artificial" subtitle="Conecte-se ao WhatsApp e utilize a IA para interações." />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <WhatsAppConnectCard 
            status={connectionStatus}
            qrCodeUrl={qrCodeUrl}
            onConnect={handleConnect}
            onDisconnect={handleDisconnect}
          />
          <div className="mt-6 bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-neutral-dark mb-3">Selecionar Gestante</h3>
            {loadingContacts ? <Spinner /> : (
              <Select
                options={pregnantWomenOptions}
                value={selectedPregnantWoman?.id || ''}
                onChange={(e) => handleSelectPregnantWoman(e.target.value)}
                placeholder="Escolha uma gestante..."
              />
            )}
          </div>
        </div>

        <div className="lg:col-span-2">
          {selectedPregnantWoman ? (
            <ConversationView 
              contactName={selectedPregnantWoman.name}
              messages={messages}
              onSendMessage={handleSendMessage}
              onSendAiMessage={handleSendAiMessage}
              currentPregnantWoman={selectedPregnantWoman}
            />
          ) : (
            <div className="bg-white p-6 rounded-lg shadow h-full flex items-center justify-center">
              <p className="text-gray-500">Selecione uma gestante para visualizar ou iniciar uma conversa.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppPage;
    