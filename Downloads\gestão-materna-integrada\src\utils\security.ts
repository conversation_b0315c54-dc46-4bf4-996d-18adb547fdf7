/**
 * Utilitários de segurança para sanitização e validação de dados
 */

// Caracteres perigosos que devem ser removidos ou escapados
const DANGEROUS_CHARS = /<script|javascript:|data:|vbscript:|onload|onerror|onclick/gi;
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
};

/**
 * Escapa caracteres HTML para prevenir XSS
 */
export const escapeHtml = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text);
  }
  
  return text.replace(/[&<>"'/]/g, (char) => HTML_ENTITIES[char] || char);
};

/**
 * Remove scripts e conteúdo perigoso de strings
 */
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') {
    return String(input);
  }

  // Remove caracteres perigosos
  let sanitized = input.replace(DANGEROUS_CHARS, '');
  
  // Remove caracteres de controle (exceto quebras de linha e tabs)
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  
  // Limita o tamanho da string
  if (sanitized.length > 10000) {
    sanitized = sanitized.substring(0, 10000);
  }
  
  return sanitized.trim();
};

/**
 * Sanitiza um objeto recursivamente
 */
export const sanitizeObject = <T extends Record<string, any>>(obj: T): T => {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const sanitized = {} as T;
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key as keyof T] = sanitizeInput(value) as T[keyof T];
    } else if (Array.isArray(value)) {
      sanitized[key as keyof T] = value.map(item => 
        typeof item === 'string' ? sanitizeInput(item) : 
        typeof item === 'object' ? sanitizeObject(item) : item
      ) as T[keyof T];
    } else if (value && typeof value === 'object') {
      sanitized[key as keyof T] = sanitizeObject(value);
    } else {
      sanitized[key as keyof T] = value;
    }
  }
  
  return sanitized;
};

/**
 * Valida se um email é seguro (formato básico)
 */
export const isValidEmail = (email: string): boolean => {
  if (typeof email !== 'string') return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

/**
 * Valida se um telefone brasileiro é válido
 */
export const isValidBrazilianPhone = (phone: string): boolean => {
  if (typeof phone !== 'string') return false;
  
  // Remove caracteres não numéricos
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Verifica se tem 10 ou 11 dígitos (com ou sem código do país)
  if (cleanPhone.length === 10 || cleanPhone.length === 11) {
    return true;
  }
  
  // Com código do país (+55)
  if (cleanPhone.length === 13 && cleanPhone.startsWith('55')) {
    return true;
  }
  
  return false;
};

/**
 * Normaliza um telefone brasileiro removendo formatação
 */
export const normalizeBrazilianPhone = (phone: string): string => {
  if (typeof phone !== 'string') return '';
  
  // Remove tudo que não é número
  let normalized = phone.replace(/\D/g, '');
  
  // Remove código do país se presente
  if (normalized.startsWith('55') && normalized.length === 13) {
    normalized = normalized.substring(2);
  }
  
  return normalized;
};

/**
 * Valida se uma data está em um range seguro
 */
export const isValidDateRange = (dateString: string, maxFutureMonths = 12): boolean => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const maxFuture = new Date();
    maxFuture.setMonth(maxFuture.getMonth() + maxFutureMonths);
    
    // Verifica se é uma data válida
    if (isNaN(date.getTime())) return false;
    
    // Verifica se está no futuro mas não muito distante
    return date > now && date <= maxFuture;
  } catch {
    return false;
  }
};

/**
 * Gera um token CSRF simples
 */
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Valida um token CSRF
 */
export const validateCSRFToken = (token: string, expectedToken: string): boolean => {
  if (typeof token !== 'string' || typeof expectedToken !== 'string') {
    return false;
  }
  
  // Comparação segura contra timing attacks
  if (token.length !== expectedToken.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ expectedToken.charCodeAt(i);
  }
  
  return result === 0;
};

/**
 * Limita o tamanho de upload de arquivo
 */
export const validateFileSize = (file: File, maxSizeMB = 5): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};

/**
 * Valida tipos de arquivo permitidos
 */
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type);
};

/**
 * Headers de segurança recomendados
 */
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;",
} as const;

/**
 * Configuração de rate limiting
 */
export const RATE_LIMIT_CONFIG = {
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // máximo 100 requests por janela
  message: 'Muitas tentativas. Tente novamente em 15 minutos.',
  standardHeaders: true,
  legacyHeaders: false,
} as const;

/**
 * Valida se uma string contém apenas caracteres seguros
 */
export const containsOnlySafeChars = (input: string): boolean => {
  // Rejeita caracteres perigosos explicitamente
  const dangerousCharsRegex = /<script|javascript:|data:|vbscript:|on\w+=/i;
  if (dangerousCharsRegex.test(input)) {
    return false;
  }

  // Permite letras, números, espaços e alguns caracteres especiais seguros
  const safeCharsRegex = /^[a-zA-ZÀ-ÿ0-9\s\-_.,!?()@#$%&*+=:;'"\/\[\]{}|\\~`^]*$/;
  return safeCharsRegex.test(input);
};

/**
 * Remove caracteres potencialmente perigosos mantendo a legibilidade
 */
export const cleanText = (text: string): string => {
  if (typeof text !== 'string') return '';
  
  return text
    .replace(/[<>]/g, '') // Remove < e >
    .replace(/javascript:/gi, '') // Remove javascript:
    .replace(/data:/gi, '') // Remove data:
    .replace(/vbscript:/gi, '') // Remove vbscript:
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};
