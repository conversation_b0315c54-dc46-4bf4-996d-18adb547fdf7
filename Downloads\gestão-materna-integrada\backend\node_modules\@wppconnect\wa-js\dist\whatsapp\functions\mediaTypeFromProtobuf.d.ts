/*!
 * Copyright 2021 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/** @whatsapp 95318
 * @whatsapp 58853 >= 2.2218.4
 * @whatsapp 458853 >= 2.2222.8
 * @whatsapp 760210 >= 2.2228.4
 */
export declare function mediaTypeFromProtobuf(protoMessage: {
    [key: string]: any;
}): string | null;
