import React, { useState, useRef, useEffect, cloneElement } from 'react';
import { Message, PregnantWoman, GroundingChunk } from '../../src/types';
import MessageInput from './MessageInput';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale/pt-BR';
import * as geminiService from '../../src/services/geminiService';
import { ICONS } from '../../src/constants';
import Spinner from '../shared/Spinner';
import Button from '../shared/Button';

interface ConversationViewProps {
  contactName: string;
  messages: Message[];
  onSendMessage: (text: string) => Message; // Returns the sent message
  onSendAiMessage: (text: string) => Message; // For AI message
  currentPregnantWoman: PregnantWoman;
}

const ConversationView: React.FC<ConversationViewProps> = ({ contactName, messages, onSendMessage, onSendAiMessage, currentPregnantWoman }) => {
  const [aiLoading, setAiLoading] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [aiSuggestion, setAiSuggestion] = useState<string | null>(null);
  const [groundingChunks, setGroundingChunks] = useState<GroundingChunk[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const generateContextForAI = (): string => {
    let context = `Informações da gestante: Nome: ${currentPregnantWoman.name}, DPP: ${currentPregnantWoman.dueDate}.`;
    if (currentPregnantWoman.observations) {
      context += ` Observações: ${currentPregnantWoman.observations}.`;
    }
    context += "\n\nHistórico da conversa recente (últimas 5 mensagens):\n";
    messages.slice(-5).forEach(msg => {
      context += `${msg.sender === 'user' ? 'Assistente' : msg.sender === 'ai' ? 'Sugestão IA' : contactName}: ${msg.text}\n`;
    });
    return context;
  };

  const handleGetAISuggestion = async (promptType: 'general' | 'empathy' | 'information' | 'next_step') => {
    setAiLoading(true);
    setAiError(null);
    setAiSuggestion(null);
    setGroundingChunks([]);

    const conversationContext = generateContextForAI();
    let systemInstruction = "Você é um assistente virtual para suporte a gestantes. Seja empático, claro e forneça informações úteis e seguras. Responda em português brasileiro.";
    let userPrompt = "";

    switch (promptType) {
        case 'empathy':
            userPrompt = `Com base no contexto, formule uma resposta empática para a última mensagem da gestante.`;
            break;
        case 'information':
            userPrompt = `A gestante parece precisar de informação. Com base no contexto, forneça uma informação relevante e útil. Se for uma pergunta sobre saúde, sugira consultar um médico. Se for uma dúvida que pode ser respondida com informações gerais, responda. Se for algo recente ou que precise de dados da web, avise que irá pesquisar.`;
            // For this case, we might want to use Google Search grounding
            break;
        case 'next_step':
            userPrompt = `Qual seria um bom próximo passo ou pergunta para continuar o acompanhamento desta gestante, baseado no contexto atual?`;
            break;
        case 'general':
        default:
            userPrompt = `Sugira uma resposta apropriada para a última mensagem da gestante, considerando todo o contexto fornecido.`;
            break;
    }
    
    const fullPrompt = `${conversationContext}\n\n Tarefa para IA: ${userPrompt}`;

    try {
      // Example of how to potentially use Google Search for information requests
      let response;
      if (promptType === 'information' && (messages.slice(-1)[0]?.text.includes('notícia') || messages.slice(-1)[0]?.text.includes('recente'))) {
          response = await geminiService.generateTextWithGoogleSearch(fullPrompt);
          if(response.groundingChunks) setGroundingChunks(response.groundingChunks);
      } else {
          response = await geminiService.generateText(fullPrompt, systemInstruction);
      }

      setAiSuggestion(response.text);
    } catch (error) {
      console.error("AI suggestion error:", error);
      setAiError(error instanceof Error ? error.message : "Falha ao obter sugestão da IA.");
    } finally {
      setAiLoading(false);
    }
  };

  const MessageBubble: React.FC<{ message: Message }> = ({ message }) => {
    const isUser = message.sender === 'user';
    const isAI = message.sender === 'ai';
    
    const bubbleClasses = isUser || isAI
      ? 'bg-primary text-white self-end rounded-l-xl rounded-tr-xl'
      : 'bg-gray-200 text-neutral-dark self-start rounded-r-xl rounded-tl-xl';
    
    const aiPrefix = isAI ? <span className="font-semibold text-xs block mb-1 opacity-80">Sugestão IA Enviada:</span> : null;

    return (
      <div className={`max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl px-4 py-2 my-1 ${bubbleClasses} shadow-sm`}>
        {aiPrefix}
        <p className="text-sm whitespace-pre-wrap">{message.text}</p>
        <span className="text-xs opacity-70 mt-1 block text-right">
          {format(new Date(message.timestamp), "HH:mm", { locale: ptBR })}
        </span>
      </div>
    );
  };


  return (
    <div className="bg-white rounded-lg shadow flex flex-col h-[calc(100vh-12rem)] max-h-[700px]"> {/* Adjusted height */}
      <header className="bg-gray-50 p-4 border-b border-gray-200 rounded-t-lg">
        <h3 className="text-lg font-semibold text-neutral-dark">{contactName}</h3>
        <p className="text-xs text-gray-500">DPP: {new Date(currentPregnantWoman.dueDate).toLocaleDateString('pt-BR', { timeZone: 'UTC' })}</p>
      </header>

      <div className="flex-1 p-4 overflow-y-auto space-y-2 bg-gray-100">
        {messages.map(msg => <MessageBubble key={msg.id} message={msg} />)}
        <div ref={messagesEndRef} />
      </div>

      {aiLoading && <div className="p-2 border-t"><Spinner size="sm"/> <p className="text-xs text-center text-gray-500">IA pensando...</p></div>}
      {aiError && <p className="p-2 text-xs text-red-500 text-center border-t">{aiError}</p>}
      {aiSuggestion && (
        <div className="p-3 border-t bg-blue-50">
          <div className="flex justify-between items-center mb-1">
            <p className="text-xs font-semibold text-primary">Sugestão da IA:</p>
            <button onClick={() => setAiSuggestion(null)} className="text-xs text-gray-400 hover:text-gray-600">&times; Dispensar</button>
          </div>
          <p className="text-sm text-neutral-dark whitespace-pre-wrap">{aiSuggestion}</p>
          {groundingChunks && groundingChunks.length > 0 && (
            <div className="mt-2">
              <p className="text-xs font-semibold text-gray-600">Fontes (Google Search):</p>
              <ul className="list-disc list-inside text-xs">
                {groundingChunks.map((chunk, index) => (
                  chunk.web && <li key={index}><a href={chunk.web.uri} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">{chunk.web.title || chunk.web.uri}</a></li>
                ))}
              </ul>
            </div>
          )}
          <Button 
            size="sm" 
            variant="secondary" 
            onClick={() => { onSendAiMessage(aiSuggestion); setAiSuggestion(null); setGroundingChunks([]); }} 
            className="mt-2 w-full"
            disabled={!process.env.API_KEY}
          >
            Enviar Resposta da IA
          </Button>
        </div>
      )}

      <div className="p-2 border-t border-gray-200 bg-gray-50">
        <div className="flex space-x-1 mb-2 justify-end">
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('general')} disabled={aiLoading || !process.env.API_KEY} title="Sugestão Geral">{cloneElement(ICONS.ai, {className:"w-4 h-4"})}</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('empathy')} disabled={aiLoading || !process.env.API_KEY} title="Resposta Empática">Empatia</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('information')} disabled={aiLoading || !process.env.API_KEY} title="Fornecer Informação">Info</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('next_step')} disabled={aiLoading || !process.env.API_KEY} title="Próximo Passo">Ação</Button>
        </div>
        <MessageInput onSend={onSendMessage} disabled={!process.env.API_KEY && !aiSuggestion} />
         {!process.env.API_KEY && <p className="text-xs text-red-500 mt-1 text-center">API Key não configurada. Funções de IA e envio de mensagens podem estar limitadas.</p>}
      </div>
    </div>
  );
};

export default ConversationView;