"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Schedule = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const ScheduleSchema = new mongoose_1.Schema({
    contact: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Contact',
        required: true,
        index: true
    },
    type: {
        type: String,
        enum: ['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up'],
        required: true,
        index: true
    },
    // Configurações de agendamento
    scheduledFor: {
        type: Date,
        required: true,
        index: true
    },
    frequency: {
        type: String,
        enum: ['daily', 'weekly', 'biweekly', 'monthly', 'custom']
    },
    customInterval: {
        type: Number,
        min: 1,
        max: 365
    },
    // Conteúdo da mensagem
    title: {
        type: String,
        required: true,
        maxlength: [200, 'Título deve ter no máximo 200 caracteres']
    },
    message: {
        type: String,
        required: true,
        maxlength: [2000, 'Mensagem deve ter no máximo 2000 caracteres']
    },
    messageType: {
        type: String,
        enum: ['text', 'educational', 'motivational', 'reminder', 'question'],
        default: 'text'
    },
    // Condições para envio
    gestationalWeekMin: {
        type: Number,
        min: 0,
        max: 45
    },
    gestationalWeekMax: {
        type: Number,
        min: 0,
        max: 45
    },
    pregnancyStage: {
        type: String,
        enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
    },
    isHighRisk: Boolean,
    // Status e controle
    status: {
        type: String,
        enum: ['pending', 'sent', 'failed', 'cancelled', 'completed'],
        default: 'pending',
        index: true
    },
    sentAt: Date,
    responseReceived: {
        type: Boolean,
        default: false
    },
    responseContent: String,
    // Configurações avançadas
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    requiresResponse: {
        type: Boolean,
        default: false
    },
    autoReschedule: {
        type: Boolean,
        default: false
    },
    maxAttempts: {
        type: Number,
        default: 3,
        min: 1,
        max: 10
    },
    attempts: {
        type: Number,
        default: 0
    },
    // Personalização
    personalized: {
        type: Boolean,
        default: true
    },
    variables: {
        type: Map,
        of: String
    },
    // Dados do criador
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    lastModifiedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});
// Índices para performance
ScheduleSchema.index({ scheduledFor: 1, status: 1 });
ScheduleSchema.index({ contact: 1, type: 1 });
ScheduleSchema.index({ pregnancyStage: 1, status: 1 });
ScheduleSchema.index({ createdBy: 1 });
// Método para verificar se deve ser enviado
ScheduleSchema.methods.shouldSend = function () {
    const now = new Date();
    // Verificar se já passou da hora agendada
    if (this.scheduledFor > now)
        return false;
    // Verificar status
    if (this.status !== 'pending')
        return false;
    // Verificar tentativas máximas
    if (this.attempts >= this.maxAttempts)
        return false;
    return true;
};
// Método para personalizar mensagem
ScheduleSchema.methods.personalize = function () {
    if (!this.personalized || !this.variables) {
        return this.message;
    }
    let personalizedMessage = this.message;
    // Substituir variáveis na mensagem
    for (const [key, value] of this.variables) {
        const placeholder = `{{${key}}}`;
        personalizedMessage = personalizedMessage.replace(new RegExp(placeholder, 'g'), value);
    }
    return personalizedMessage;
};
// Método para marcar como enviado
ScheduleSchema.methods.markAsSent = async function () {
    this.status = 'sent';
    this.sentAt = new Date();
    this.attempts += 1;
    await this.save();
};
// Método para reagendar
ScheduleSchema.methods.reschedule = async function (days) {
    const newDate = new Date(this.scheduledFor);
    newDate.setDate(newDate.getDate() + days);
    this.scheduledFor = newDate;
    this.status = 'pending';
    this.attempts = 0;
    await this.save();
};
// Middleware para validação de semanas gestacionais
ScheduleSchema.pre('save', function (next) {
    if (this.gestationalWeekMin && this.gestationalWeekMax) {
        if (this.gestationalWeekMin > this.gestationalWeekMax) {
            next(new Error('Semana mínima não pode ser maior que a máxima'));
            return;
        }
    }
    next();
});
exports.Schedule = mongoose_1.default.model('Schedule', ScheduleSchema);
