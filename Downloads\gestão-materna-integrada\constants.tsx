import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  PlusIcon,
  PencilSquareIcon,
  TrashIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  CheckIcon,
  XMarkIcon,
  BellIcon,
  CalendarIcon,
  ClockIcon,
  PhoneIcon,
  EnvelopeIcon,
  HeartIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { Message, PregnantWoman } from './types';

export const APP_NAME = "Rafaela Cuida";

export const DEFAULT_API_KEY_NOTICE = "API Key não configurada. Verifique as variáveis de ambiente.";

export const ICONS = {
  dashboard: <HomeIcon className="w-5 h-5" />,
  pregnant: <UserGroupIcon className="w-5 h-5" />,
  whatsapp: <ChatBubbleLeftRightIcon className="w-5 h-5" />,
  settings: <Cog6ToothIcon className="w-5 h-5" />,
  add: <PlusIcon className="w-5 h-5" />,
  edit: <PencilSquareIcon className="w-5 h-5" />,
  delete: <TrashIcon className="w-5 h-5" />,
  upload: <ArrowUpTrayIcon className="w-5 h-5" />,
  download: <ArrowDownTrayIcon className="w-5 h-5" />,
  check: <CheckIcon className="w-5 h-5" />,
  x: <XMarkIcon className="w-5 h-5" />,
  notification: <BellIcon className="w-5 h-5" />,
  calendar: <CalendarIcon className="w-5 h-5" />,
  clock: <ClockIcon className="w-5 h-5" />,
  phone: <PhoneIcon className="w-5 h-5" />,
  email: <EnvelopeIcon className="w-5 h-5" />,
  followUp: <HeartIcon className="w-5 h-5" />,
  search: <MagnifyingGlassIcon className="w-5 h-5" />,
  warning: <ExclamationTriangleIcon className="w-5 h-5" />,
  info: <InformationCircleIcon className="w-5 h-5" />,
  xCircle: <XCircleIcon className="w-5 h-5" />,
  checkCircle: <CheckCircleIcon className="w-5 h-5" />
};

export const MOCK_PREGNANT_WOMEN: PregnantWoman[] = [
  { id: '1', name: 'Ana Silva', dueDate: '2024-10-15', phone: '11987654321', email: '<EMAIL>', createdAt: '2024-02-01T10:00:00Z', age: 30, observations: 'Primeira gestação, acompanhamento regular.' },
  { id: '2', name: 'Beatriz Costa', dueDate: '2024-12-01', phone: '21912345678', email: '<EMAIL>', createdAt: '2024-03-15T14:30:00Z', age: 25, observations: 'Gestação gemelar.'},
  { id: '3', name: 'Carla Oliveira', dueDate: '2025-01-20', phone: '31999998888', createdAt: '2024-05-10T09:00:00Z', age: 34 },
  { id: '4', name: 'Daniela Pereira', dueDate: '2024-11-05', phone: '41977776666', email: '<EMAIL>', createdAt: '2024-04-01T11:00:00Z', age: 28 },
  { id: '5', name: 'Eduarda Ferreira', dueDate: '2025-02-10', phone: '51965432109', createdAt: '2024-06-20T16:00:00Z', age: 31, observations: 'Diabetes gestacional.' },
];

export const MOCK_MESSAGES: Message[] = [
    { id: 'm1', sender: 'gestante', text: 'Olá, tudo bem?', timestamp: new Date(Date.now() - 3600000 * 2).toISOString() },
    { id: 'm2', sender: 'user', text: 'Olá! Tudo ótimo e com você? Como está se sentindo hoje?', timestamp: new Date(Date.now() - 3600000 * 1.5).toISOString() },
    { id: 'm3', sender: 'gestante', text: 'Estou bem, um pouco cansada apenas.', timestamp: new Date(Date.now() - 3600000 * 1).toISOString() },
    { id: 'm4', sender: 'ai', text: 'Lembre-se de descansar e manter uma boa hidratação. Se precisar de algo, é só chamar!', timestamp: new Date(Date.now() - 3600000 * 0.5).toISOString() },
];
