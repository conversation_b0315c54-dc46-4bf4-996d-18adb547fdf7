"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Message_1 = require("../models/Message");
const Contact_1 = require("../models/Contact");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validações
const messageValidation = [
    (0, express_validator_1.body)('content')
        .trim()
        .isLength({ min: 1, max: 4000 })
        .withMessage('Conteúdo deve ter entre 1 e 4000 caracteres'),
    (0, express_validator_1.body)('contact')
        .isMongoId()
        .withMessage('ID do contato inválido'),
    (0, express_validator_1.body)('type')
        .optional()
        .isIn(['text', 'image', 'audio', 'video', 'document', 'location', 'contact_card', 'sticker'])
        .withMessage('Tipo de mensagem inválido')
];
// GET /api/messages - Listar mensagens com filtros
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('contact').optional().isMongoId(),
    (0, express_validator_1.query)('fromMe').optional().isBoolean(),
    (0, express_validator_1.query)('category').optional().isIn(['medical', 'emotional', 'administrative', 'emergency', 'routine']),
    (0, express_validator_1.query)('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    (0, express_validator_1.query)('sentiment').optional().isIn(['positive', 'negative', 'neutral', 'urgent']),
    (0, express_validator_1.query)('unprocessed').optional().isBoolean(),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const contact = req.query.contact;
        const fromMe = req.query.fromMe === 'true';
        const category = req.query.category;
        const priority = req.query.priority;
        const sentiment = req.query.sentiment;
        const unprocessed = req.query.unprocessed === 'true';
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        // Construir filtros
        const filters = {};
        if (contact)
            filters.contact = contact;
        if (req.query.fromMe !== undefined)
            filters.fromMe = fromMe;
        if (category)
            filters.category = category;
        if (priority)
            filters.priority = priority;
        if (sentiment)
            filters['sentiment.type'] = sentiment;
        if (unprocessed)
            filters.processedBy = { $exists: false };
        // Filtro de data
        if (startDate || endDate) {
            filters.timestamp = {};
            if (startDate)
                filters.timestamp.$gte = new Date(startDate);
            if (endDate)
                filters.timestamp.$lte = new Date(endDate);
        }
        // Executar consulta
        const skip = (page - 1) * limit;
        const [messages, total] = await Promise.all([
            Message_1.Message.find(filters)
                .sort({ timestamp: -1 })
                .skip(skip)
                .limit(limit)
                .populate('contact', 'name phone pregnancyStage priority')
                .populate('processedBy', 'name email')
                .lean(),
            Message_1.Message.countDocuments(filters)
        ]);
        res.json({
            messages,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            filters: {
                contact,
                fromMe: req.query.fromMe !== undefined ? fromMe : undefined,
                category,
                priority,
                sentiment,
                unprocessed: req.query.unprocessed !== undefined ? unprocessed : undefined,
                startDate,
                endDate
            }
        });
    }
    catch (error) {
        console.error('Erro ao listar mensagens:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/messages/:id - Obter mensagem específica
router.get('/:id', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), async (req, res) => {
    try {
        const message = await Message_1.Message.findById(req.params.id)
            .populate('contact', 'name phone pregnancyStage priority')
            .populate('processedBy', 'name email');
        if (!message) {
            return res.status(404).json({
                error: 'Mensagem não encontrada',
                code: 'MESSAGE_NOT_FOUND'
            });
        }
        res.json({ message });
    }
    catch (error) {
        console.error('Erro ao obter mensagem:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/messages - Criar nova mensagem
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), messageValidation, (0, auth_1.auditLog)('CREATE_MESSAGE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        // Verificar se contato existe
        const contact = await Contact_1.Contact.findById(req.body.contact);
        if (!contact) {
            return res.status(404).json({
                error: 'Contato não encontrado',
                code: 'CONTACT_NOT_FOUND'
            });
        }
        // Criar mensagem
        const messageData = {
            ...req.body,
            fromMe: true, // Mensagens criadas via API são sempre "de nós"
            timestamp: new Date()
        };
        const message = new Message_1.Message(messageData);
        // Analisar conteúdo automaticamente
        await message.analyzeContent();
        await message.generateSuggestions();
        await message.save();
        // Atualizar última interação do contato
        contact.lastInteraction = new Date();
        await contact.save();
        await message.populate('contact', 'name phone pregnancyStage priority');
        res.status(201).json({
            message: 'Mensagem criada com sucesso',
            data: message
        });
    }
    catch (error) {
        console.error('Erro ao criar mensagem:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// PUT /api/messages/:id/process - Marcar mensagem como processada
router.put('/:id/process', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), [
    (0, express_validator_1.body)('notes')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Notas devem ter no máximo 1000 caracteres')
], (0, auth_1.auditLog)('PROCESS_MESSAGE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const message = await Message_1.Message.findById(req.params.id);
        if (!message) {
            return res.status(404).json({
                error: 'Mensagem não encontrada',
                code: 'MESSAGE_NOT_FOUND'
            });
        }
        await message.markAsProcessed(req.user._id, req.body.notes);
        await message.populate('contact', 'name phone pregnancyStage priority');
        await message.populate('processedBy', 'name email');
        res.json({
            message: 'Mensagem marcada como processada',
            data: message
        });
    }
    catch (error) {
        console.error('Erro ao processar mensagem:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/messages/:id/analyze - Re-analisar mensagem
router.post('/:id/analyze', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), (0, auth_1.auditLog)('ANALYZE_MESSAGE'), async (req, res) => {
    try {
        const message = await Message_1.Message.findById(req.params.id);
        if (!message) {
            return res.status(404).json({
                error: 'Mensagem não encontrada',
                code: 'MESSAGE_NOT_FOUND'
            });
        }
        // Re-analisar conteúdo
        await message.analyzeContent();
        await message.generateSuggestions();
        await message.save();
        await message.populate('contact', 'name phone pregnancyStage priority');
        res.json({
            message: 'Mensagem re-analisada com sucesso',
            data: {
                sentiment: message.sentiment,
                category: message.category,
                priority: message.priority,
                tags: message.tags,
                suggestions: message.suggestions
            }
        });
    }
    catch (error) {
        console.error('Erro ao re-analisar mensagem:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
