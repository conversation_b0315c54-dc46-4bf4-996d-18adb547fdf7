"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Schedule_1 = require("../models/Schedule");
const MessageTemplate_1 = require("../models/MessageTemplate");
const Contact_1 = require("../models/Contact");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Validações
const scheduleValidation = [
    (0, express_validator_1.body)('contact')
        .isMongoId()
        .withMessage('ID do contato inválido'),
    (0, express_validator_1.body)('type')
        .isIn(['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up'])
        .withMessage('Tipo de agendamento inválido'),
    (0, express_validator_1.body)('scheduledFor')
        .isISO8601()
        .withMessage('Data de agendamento inválida'),
    (0, express_validator_1.body)('title')
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Título deve ter entre 1 e 200 caracteres'),
    (0, express_validator_1.body)('message')
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage('Mensagem deve ter entre 1 e 2000 caracteres')
];
// GET /api/schedules - Listar agendamentos
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('contact').optional().isMongoId(),
    (0, express_validator_1.query)('type').optional().isIn(['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up']),
    (0, express_validator_1.query)('status').optional().isIn(['pending', 'sent', 'failed', 'cancelled', 'completed']),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const contact = req.query.contact;
        const type = req.query.type;
        const status = req.query.status;
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        // Construir filtros
        const filters = {};
        if (contact)
            filters.contact = contact;
        if (type)
            filters.type = type;
        if (status)
            filters.status = status;
        // Filtro de data
        if (startDate || endDate) {
            filters.scheduledFor = {};
            if (startDate)
                filters.scheduledFor.$gte = new Date(startDate);
            if (endDate)
                filters.scheduledFor.$lte = new Date(endDate);
        }
        // Filtro por usuário (não-admin vê apenas seus agendamentos)
        if (req.user.role !== 'admin') {
            filters.createdBy = req.user._id;
        }
        const skip = (page - 1) * limit;
        const [schedules, total] = await Promise.all([
            Schedule_1.Schedule.find(filters)
                .sort({ scheduledFor: -1 })
                .skip(skip)
                .limit(limit)
                .populate('contact', 'name phone pregnancyStage priority')
                .populate('createdBy', 'name email')
                .lean(),
            Schedule_1.Schedule.countDocuments(filters)
        ]);
        res.json({
            schedules,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            filters: {
                contact,
                type,
                status,
                startDate,
                endDate
            }
        });
    }
    catch (error) {
        console.error('Erro ao listar agendamentos:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/schedules/:id - Obter agendamento específico
router.get('/:id', auth_1.authenticate, (0, auth_1.authorize)('read:messages'), async (req, res) => {
    try {
        const schedule = await Schedule_1.Schedule.findById(req.params.id)
            .populate('contact', 'name phone pregnancyStage priority')
            .populate('createdBy', 'name email')
            .populate('lastModifiedBy', 'name email');
        if (!schedule) {
            return res.status(404).json({
                error: 'Agendamento não encontrado',
                code: 'SCHEDULE_NOT_FOUND'
            });
        }
        // Verificar permissão
        if (req.user.role !== 'admin' && schedule.createdBy._id.toString() !== req.user._id.toString()) {
            return res.status(403).json({
                error: 'Acesso negado',
                code: 'ACCESS_DENIED'
            });
        }
        res.json({ schedule });
    }
    catch (error) {
        console.error('Erro ao obter agendamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/schedules - Criar agendamento
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), scheduleValidation, (0, auth_1.auditLog)('CREATE_SCHEDULE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        // Verificar se contato existe
        const contact = await Contact_1.Contact.findById(req.body.contact);
        if (!contact) {
            return res.status(404).json({
                error: 'Contato não encontrado',
                code: 'CONTACT_NOT_FOUND'
            });
        }
        // Criar agendamento
        const scheduleData = {
            ...req.body,
            createdBy: req.user._id,
            status: 'pending',
            attempts: 0
        };
        const schedule = new Schedule_1.Schedule(scheduleData);
        await schedule.save();
        await schedule.populate('contact', 'name phone pregnancyStage priority');
        await schedule.populate('createdBy', 'name email');
        res.status(201).json({
            message: 'Agendamento criado com sucesso',
            schedule
        });
    }
    catch (error) {
        console.error('Erro ao criar agendamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// PUT /api/schedules/:id - Atualizar agendamento
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), scheduleValidation, (0, auth_1.auditLog)('UPDATE_SCHEDULE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const schedule = await Schedule_1.Schedule.findById(req.params.id);
        if (!schedule) {
            return res.status(404).json({
                error: 'Agendamento não encontrado',
                code: 'SCHEDULE_NOT_FOUND'
            });
        }
        // Verificar permissão
        if (req.user.role !== 'admin' && schedule.createdBy.toString() !== req.user._id.toString()) {
            return res.status(403).json({
                error: 'Acesso negado',
                code: 'ACCESS_DENIED'
            });
        }
        // Não permitir editar agendamentos já enviados
        if (schedule.status === 'sent' || schedule.status === 'completed') {
            return res.status(400).json({
                error: 'Não é possível editar agendamentos já enviados',
                code: 'SCHEDULE_ALREADY_SENT'
            });
        }
        // Atualizar agendamento
        Object.assign(schedule, req.body);
        schedule.lastModifiedBy = req.user._id;
        await schedule.save();
        await schedule.populate('contact', 'name phone pregnancyStage priority');
        await schedule.populate('createdBy', 'name email');
        res.json({
            message: 'Agendamento atualizado com sucesso',
            schedule
        });
    }
    catch (error) {
        console.error('Erro ao atualizar agendamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// DELETE /api/schedules/:id - Cancelar agendamento
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), (0, auth_1.auditLog)('CANCEL_SCHEDULE'), async (req, res) => {
    try {
        const schedule = await Schedule_1.Schedule.findById(req.params.id);
        if (!schedule) {
            return res.status(404).json({
                error: 'Agendamento não encontrado',
                code: 'SCHEDULE_NOT_FOUND'
            });
        }
        // Verificar permissão
        if (req.user.role !== 'admin' && schedule.createdBy.toString() !== req.user._id.toString()) {
            return res.status(403).json({
                error: 'Acesso negado',
                code: 'ACCESS_DENIED'
            });
        }
        // Cancelar agendamento
        schedule.status = 'cancelled';
        schedule.lastModifiedBy = req.user._id;
        await schedule.save();
        res.json({
            message: 'Agendamento cancelado com sucesso'
        });
    }
    catch (error) {
        console.error('Erro ao cancelar agendamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/schedules/bulk - Criar agendamentos em massa
router.post('/bulk', auth_1.authenticate, (0, auth_1.authorize)('send:bulk_messages'), [
    (0, express_validator_1.body)('contacts')
        .isArray({ min: 1, max: 100 })
        .withMessage('Lista de contatos deve ter entre 1 e 100 itens'),
    (0, express_validator_1.body)('template')
        .isMongoId()
        .withMessage('ID do template inválido'),
    (0, express_validator_1.body)('scheduledFor')
        .isISO8601()
        .withMessage('Data de agendamento inválida')
], (0, auth_1.auditLog)('CREATE_BULK_SCHEDULES'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { contacts, template: templateId, scheduledFor } = req.body;
        // Verificar se template existe
        const template = await MessageTemplate_1.MessageTemplate.findById(templateId);
        if (!template || !template.isActive) {
            return res.status(404).json({
                error: 'Template não encontrado ou inativo',
                code: 'TEMPLATE_NOT_FOUND'
            });
        }
        const results = {
            total: contacts.length,
            created: 0,
            failed: 0,
            details: []
        };
        // Criar agendamentos
        for (const contactId of contacts) {
            try {
                const contact = await Contact_1.Contact.findById(contactId);
                if (!contact) {
                    results.failed++;
                    results.details.push({
                        contactId,
                        status: 'failed',
                        error: 'Contato não encontrado'
                    });
                    continue;
                }
                const schedule = new Schedule_1.Schedule({
                    contact: contactId,
                    type: template.category,
                    scheduledFor: new Date(scheduledFor),
                    title: template.title,
                    message: template.content,
                    messageType: 'text',
                    priority: template.priority,
                    requiresResponse: template.requiresResponse,
                    personalized: true,
                    createdBy: req.user._id
                });
                await schedule.save();
                results.created++;
                results.details.push({
                    contactId,
                    scheduleId: schedule._id,
                    status: 'created'
                });
            }
            catch (error) {
                results.failed++;
                results.details.push({
                    contactId,
                    status: 'failed',
                    error: error instanceof Error ? error.message : 'Erro desconhecido'
                });
            }
        }
        res.status(201).json({
            message: 'Agendamentos em massa criados',
            results
        });
    }
    catch (error) {
        console.error('Erro ao criar agendamentos em massa:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
