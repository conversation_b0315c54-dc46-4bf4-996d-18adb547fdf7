/*!
 * Copyright 2021 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Reject a incoming call
 *
 * @example
 * ```javascript
 * // Reject any incoming call
 * WPP.call.reject();
 *
 * // Reject specific call id
 * WPP.call.reject(callId);
 *
 * // Reject any incoming call
 * WPP.on('call.incoming_call', (call) => {
 *   WPP.call.reject(call.id);
 * });
 * ```
 *
 * @param   {string}  callId  The call ID, empty to reject the first one
 * @return  {[type]}          [return description]
 */
export declare function reject(callId?: string): Promise<boolean>;
