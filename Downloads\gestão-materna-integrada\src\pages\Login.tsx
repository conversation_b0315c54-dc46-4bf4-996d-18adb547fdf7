import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { authService } from '../services/authService';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Verificar se já está autenticado e redirecionar
  useEffect(() => {
    if (authService.isAuthenticated()) {
      navigate('/dashboard', { replace: true });
    }
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error('Por favor, preencha todos os campos');
      return;
    }

    setIsLoading(true);
    
    try {
      await authService.login({ email, password });
      toast.success('Login realizado com sucesso!');

      // Forçar redirecionamento
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 100);
    } catch (error: any) {
      console.error('Erro no login:', error);
      const message = error.response?.data?.message || 'Erro ao fazer login';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setIsLoading(true);
    try {
      // Limpar localStorage primeiro
      localStorage.clear();

      // Solução temporária: definir token fake diretamente
      const fakeToken = 'fake-jwt-token-admin-demo';
      const fakeUser = {
        id: 'admin-demo',
        name: 'Administrador Demo',
        email: '<EMAIL>',
        role: 'admin' as const,
        isActive: true
      };

      // Salvar no localStorage
      localStorage.setItem('auth_token', fakeToken);
      localStorage.setItem('user', JSON.stringify(fakeUser));

      toast.success('Login de demonstração realizado!');

      // Forçar redirecionamento
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 100);
    } catch (error: any) {
      console.error('Erro no login demo:', error);
      toast.error('Erro ao fazer login de demonstração');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceLogout = () => {
    localStorage.clear();
    authService.forceLogout();
    toast.info('Dados de autenticação limpos!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto h-16 w-16 bg-gradient-to-r from-pink-500 to-blue-500 rounded-full flex items-center justify-center mb-4">
              <span className="text-white text-2xl">👶</span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900">
              Gestão Materna
            </h2>
            <p className="text-gray-600 mt-2">
              Sistema Integrado de Acompanhamento Gestacional
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Senha
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors"
                placeholder="••••••••"
                disabled={isLoading}
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-pink-500 to-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Entrando...
                </div>
              ) : (
                'Entrar'
              )}
            </button>
          </form>

          {/* Demo Login */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">ou</span>
              </div>
            </div>

            <button
              onClick={handleDemoLogin}
              disabled={isLoading}
              className="mt-4 w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Carregando...' : 'Entrar como Demonstração'}
            </button>

            <button
              onClick={handleForceLogout}
              className="mt-2 w-full bg-red-100 text-red-700 py-2 px-4 rounded-lg font-medium hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all"
            >
              Limpar Dados de Autenticação
            </button>
          </div>

          {/* Info */}
          <div className="mt-8 text-center">
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Credenciais de Demonstração:
              </h3>
              <div className="text-xs text-blue-600 space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Senha:</strong> Admin123!@#</p>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="mt-8 grid grid-cols-2 gap-4 text-center">
            <div className="bg-pink-50 rounded-lg p-3">
              <div className="text-pink-500 text-lg mb-1">🤱</div>
              <div className="text-xs text-pink-700 font-medium">
                Acompanhamento Gestacional
              </div>
            </div>
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-blue-500 text-lg mb-1">📱</div>
              <div className="text-xs text-blue-700 font-medium">
                WhatsApp Integrado
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-green-500 text-lg mb-1">🤖</div>
              <div className="text-xs text-green-700 font-medium">
                IA Proativa
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3">
              <div className="text-purple-500 text-lg mb-1">📊</div>
              <div className="text-xs text-purple-700 font-medium">
                Analytics Avançados
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Sistema desenvolvido com ❤️ para o cuidado materno</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
