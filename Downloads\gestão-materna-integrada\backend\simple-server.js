const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();

// CORS para múltiplas portas
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174', 
    'http://localhost:5175',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Usuário admin em memória
const adminUser = {
  id: '1',
  name: 'Administrador',
  email: '<EMAIL>',
  password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // Admin123!@#
  role: 'admin',
  permissions: {
    canManageUsers: true,
    canManagePregnant: true,
    canViewAnalytics: true,
    canManageSettings: true
  },
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date()
};

// Rota de login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Tentativa de login:', { email });
    
    if (email !== adminUser.email) {
      console.log('❌ Email não encontrado');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    const isValidPassword = await bcrypt.compare(password, adminUser.password);
    
    if (!isValidPassword) {
      console.log('❌ Senha incorreta');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Simular token JWT
    const token = 'fake-jwt-token-' + Date.now();
    
    console.log('✅ Login bem-sucedido');
    
    res.json({
      message: 'Login realizado com sucesso',
      token,
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
        permissions: adminUser.permissions,
        lastLogin: adminUser.lastLogin
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no login:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota de health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Servidor funcionando' });
});

// Rota para obter usuário atual
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  res.json({
    user: {
      id: adminUser.id,
      name: adminUser.name,
      email: adminUser.email,
      role: adminUser.role,
      permissions: adminUser.permissions,
      lastLogin: adminUser.lastLogin,
      createdAt: adminUser.createdAt
    }
  });
});

// Rota de analytics do dashboard (dados mock)
app.get('/api/analytics/dashboard', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  // Dados mock para o dashboard (formato correto para validação)
  const mockData = {
    period: {
      type: 'month',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date()
    },
    contacts: {
      total: 25,
      highRisk: 3,
      urgent: 1,
      firstTrimester: 8,
      secondTrimester: 10,
      thirdTrimester: 5,
      postpartum: 2,
      dueSoon: 2,
      overdue: 0
    },
    messages: {
      total: 156,
      fromMe: 78,
      fromContacts: 78,
      urgent: 5,
      negative: 2,
      positive: 45,
      unprocessed: 8
    },
    categories: [
      { _id: 'consulta', count: 45 },
      { _id: 'sintomas', count: 32 },
      { _id: 'duvidas', count: 28 },
      { _id: 'emergencia', count: 5 }
    ],
    dailyActivity: [
      { _id: { year: 2024, month: 12, day: 20 }, messages: 12, fromMe: 6, fromContacts: 6 },
      { _id: { year: 2024, month: 12, day: 21 }, messages: 8, fromMe: 4, fromContacts: 4 },
      { _id: { year: 2024, month: 12, day: 22 }, messages: 15, fromMe: 7, fromContacts: 8 },
      { _id: { year: 2024, month: 12, day: 23 }, messages: 10, fromMe: 5, fromContacts: 5 }
    ],
    topContacts: [
      {
        name: 'Maria Silva',
        phone: '+5511999999999',
        pregnancyStage: 'second_trimester',
        messageCount: 25,
        lastMessage: new Date()
      },
      {
        name: 'Ana Santos',
        phone: '+5511888888888',
        pregnancyStage: 'third_trimester',
        messageCount: 18,
        lastMessage: new Date()
      }
    ]
  };

  console.log('📊 Analytics do dashboard solicitado');
  res.json(mockData);
});

// Rota de gestantes (dados mock)
app.get('/api/contacts', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const mockContacts = [
    {
      _id: '1',
      name: 'Maria Silva',
      phone: '+5511999999999',
      email: '<EMAIL>',
      pregnancyStage: 'second_trimester',
      dueDate: new Date('2025-06-15'),
      riskLevel: 'low',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: '2',
      name: 'Ana Santos',
      phone: '+5511888888888',
      email: '<EMAIL>',
      pregnancyStage: 'third_trimester',
      dueDate: new Date('2025-03-20'),
      riskLevel: 'medium',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  console.log('👥 Lista de gestantes solicitada');
  res.json(mockContacts);
});

// Rota de WhatsApp status (dados mock)
app.get('/api/whatsapp/status', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('📱 Status do WhatsApp solicitado');
  res.json({
    status: 'connected',
    qrCode: null
  });
});

// Rota de mensagens (dados mock)
app.get('/api/messages/:pregnantWomanId', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const mockMessages = [
    {
      id: '1',
      sender: 'gestante',
      text: 'Olá! Como está minha gravidez?',
      timestamp: new Date().toISOString(),
      status: 'read'
    },
    {
      id: '2',
      sender: 'user',
      text: 'Olá! Tudo está bem. Como você está se sentindo?',
      timestamp: new Date().toISOString(),
      status: 'sent'
    }
  ];

  console.log('💬 Mensagens solicitadas para gestante:', req.params.pregnantWomanId);
  res.json(mockMessages);
});

// Rota para enviar mensagem (dados mock)
app.post('/api/messages/send', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const { pregnantWomanId, text } = req.body;

  const newMessage = {
    id: Date.now().toString(),
    sender: 'user',
    text: text,
    timestamp: new Date().toISOString(),
    status: 'sent'
  };

  console.log('📤 Mensagem enviada para gestante:', pregnantWomanId, 'Texto:', text);
  res.json(newMessage);
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('🚀 Servidor simples iniciado na porta', PORT);
  console.log('✅ CORS configurado para múltiplas portas');
  console.log('🔐 Usuário admin disponível:');
  console.log('   Email: <EMAIL>');
  console.log('   Senha: Admin123!@#');
});

module.exports = app;
