const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
require('dotenv').config({ path: __dirname + '/.env' });

const app = express();

// CORS para múltiplas portas
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174', 
    'http://localhost:5175',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Conectar ao MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('✅ Conectado ao MongoDB'))
  .catch(err => console.error('❌ Erro ao conectar MongoDB:', err));

// Schema do Contact (Gestante)
const contactSchema = new mongoose.Schema({
  name: { type: String, required: true },
  phone: { type: String, required: true, unique: true },
  email: { type: String },
  pregnancyStage: {
    type: String,
    enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum'],
    default: 'first_trimester'
  },
  dueDate: Date,
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'low'
  },
  observations: String,
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Contact = mongoose.model('Contact', contactSchema);

// Schema do User
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ['admin', 'doctor', 'nurse', 'coordinator'],
    default: 'nurse'
  },
  permissions: {
    canManageUsers: { type: Boolean, default: false },
    canManagePregnant: { type: Boolean, default: true },
    canViewAnalytics: { type: Boolean, default: false },
    canManageSettings: { type: Boolean, default: false }
  },
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// Usuário admin em memória (fallback)
const adminUser = {
  id: '1',
  name: 'Administrador',
  email: '<EMAIL>',
  password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // Admin123!@#
  role: 'admin',
  permissions: {
    canManageUsers: true,
    canManagePregnant: true,
    canViewAnalytics: true,
    canManageSettings: true
  },
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date()
};

// Rota de login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Tentativa de login:', { email });
    
    if (email !== adminUser.email) {
      console.log('❌ Email não encontrado');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    const isValidPassword = await bcrypt.compare(password, adminUser.password);
    
    if (!isValidPassword) {
      console.log('❌ Senha incorreta');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Simular token JWT
    const token = 'fake-jwt-token-' + Date.now();
    
    console.log('✅ Login bem-sucedido');
    
    res.json({
      message: 'Login realizado com sucesso',
      token,
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
        permissions: adminUser.permissions,
        lastLogin: adminUser.lastLogin
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no login:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota de health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Servidor funcionando' });
});

// Rota para obter usuário atual
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  res.json({
    user: {
      id: adminUser.id,
      name: adminUser.name,
      email: adminUser.email,
      role: adminUser.role,
      permissions: adminUser.permissions,
      lastLogin: adminUser.lastLogin,
      createdAt: adminUser.createdAt
    }
  });
});

// Rota de analytics do dashboard (dados reais do MongoDB)
app.get('/api/analytics/dashboard', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  try {
    // Buscar dados reais do banco
    const totalContacts = await Contact.countDocuments({ isActive: true });
    const highRiskContacts = await Contact.countDocuments({ isActive: true, riskLevel: 'high' });
    const firstTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'first_trimester' });
    const secondTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'second_trimester' });
    const thirdTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'third_trimester' });
    const postpartum = await Contact.countDocuments({ isActive: true, pregnancyStage: 'postpartum' });

    // Dados reais do dashboard
    const realData = {
      period: {
        type: 'month',
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate: new Date()
      },
      contacts: {
        total: totalContacts,
        highRisk: highRiskContacts,
        urgent: 0, // Pode ser calculado baseado em critérios específicos
        firstTrimester: firstTrimester,
        secondTrimester: secondTrimester,
        thirdTrimester: thirdTrimester,
        postpartum: postpartum,
        dueSoon: 0, // Pode ser calculado baseado na data de parto
        overdue: 0
      },
      messages: {
        total: 4, // Número fixo por enquanto, pode ser calculado
        fromMe: 2,
        fromContacts: 2,
        urgent: 0,
        negative: 0,
        positive: 4,
        unprocessed: 0
      },
      categories: [
        { _id: 'consulta', count: 2 },
        { _id: 'sintomas', count: 1 },
        { _id: 'duvidas', count: 1 },
        { _id: 'emergencia', count: 0 }
      ],
      dailyActivity: [
        { _id: { year: 2024, month: 12, day: 20 }, messages: 1, fromMe: 1, fromContacts: 0 },
        { _id: { year: 2024, month: 12, day: 21 }, messages: 1, fromMe: 0, fromContacts: 1 },
        { _id: { year: 2024, month: 12, day: 22 }, messages: 1, fromMe: 1, fromContacts: 0 },
        { _id: { year: 2024, month: 12, day: 23 }, messages: 1, fromMe: 0, fromContacts: 1 }
      ],
      topContacts: await Contact.find({ isActive: true })
        .sort({ updatedAt: -1 })
        .limit(5)
        .select('name phone pregnancyStage updatedAt')
        .lean()
        .then(contacts => contacts.map(contact => ({
          name: contact.name,
          phone: contact.phone,
          pregnancyStage: contact.pregnancyStage,
          messageCount: Math.floor(Math.random() * 20) + 5, // Simulado por enquanto
          lastMessage: contact.updatedAt
        })))
    };

    console.log('📊 Analytics do dashboard solicitado - Gestantes encontradas:', totalContacts);
    res.json(realData);
  } catch (error) {
    console.error('❌ Erro ao buscar analytics:', error);
    res.status(500).json({
      error: 'Erro ao buscar analytics',
      code: 'DATABASE_ERROR'
    });
  }
});

// Rota de gestantes (dados reais do MongoDB)
app.get('/api/contacts', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  try {
    const contacts = await Contact.find({ isActive: true }).sort({ createdAt: -1 });
    console.log('👥 Lista de gestantes solicitada - Encontradas:', contacts.length);
    res.json(contacts);
  } catch (error) {
    console.error('❌ Erro ao buscar gestantes:', error);
    res.status(500).json({
      error: 'Erro ao buscar gestantes',
      code: 'DATABASE_ERROR'
    });
  }
});

// Rota de WhatsApp status (dados mock)
app.get('/api/whatsapp/status', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('📱 Status do WhatsApp solicitado');
  res.json({
    status: 'connected',
    qrCode: null
  });
});

// Rota de mensagens (dados mock)
app.get('/api/messages/:pregnantWomanId', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const mockMessages = [
    {
      id: '1',
      sender: 'gestante',
      text: 'Olá! Como está minha gravidez?',
      timestamp: new Date().toISOString(),
      status: 'read'
    },
    {
      id: '2',
      sender: 'user',
      text: 'Olá! Tudo está bem. Como você está se sentindo?',
      timestamp: new Date().toISOString(),
      status: 'sent'
    }
  ];

  console.log('💬 Mensagens solicitadas para gestante:', req.params.pregnantWomanId);
  res.json(mockMessages);
});

// Rota para enviar mensagem (dados mock)
app.post('/api/messages/send', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || !token.startsWith('fake-jwt-token-')) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const { pregnantWomanId, text } = req.body;

  const newMessage = {
    id: Date.now().toString(),
    sender: 'user',
    text: text,
    timestamp: new Date().toISOString(),
    status: 'sent'
  };

  console.log('📤 Mensagem enviada para gestante:', pregnantWomanId, 'Texto:', text);
  res.json(newMessage);
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('🚀 Servidor simples iniciado na porta', PORT);
  console.log('✅ CORS configurado para múltiplas portas');
  console.log('🔐 Usuário admin disponível:');
  console.log('   Email: <EMAIL>');
  console.log('   Senha: Admin123!@#');
});

module.exports = app;
