const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
require('dotenv').config({ path: __dirname + '/.env' });

const app = express();

// CORS para múltiplas portas
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174', 
    'http://localhost:5175',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Conectar ao MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('✅ Conectado ao MongoDB'))
  .catch(err => console.error('❌ Erro ao conectar MongoDB:', err));

// Schema do Contact (Gestante)
const contactSchema = new mongoose.Schema({
  name: { type: String, required: true },
  phone: { type: String, required: true, unique: true },
  email: { type: String },
  pregnancyStage: {
    type: String,
    enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum'],
    default: 'first_trimester'
  },
  dueDate: Date,
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'low'
  },
  observations: String,
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Contact = mongoose.model('Contact', contactSchema);

// Schema do User
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ['admin', 'doctor', 'nurse', 'coordinator'],
    default: 'nurse'
  },
  permissions: {
    canManageUsers: { type: Boolean, default: false },
    canManagePregnant: { type: Boolean, default: true },
    canViewAnalytics: { type: Boolean, default: false },
    canManageSettings: { type: Boolean, default: false }
  },
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// Gerar hash da senha na inicialização
const generateAdminUser = async () => {
  const hashedPassword = await bcrypt.hash('Admin123!@#', 12);
  console.log('🔒 Hash gerado para Admin123!@#:', hashedPassword);

  return {
    id: '1',
    name: 'Administrador',
    email: '<EMAIL>',
    password: hashedPassword,
    role: 'admin',
    permissions: {
      canManageUsers: true,
      canManagePregnant: true,
      canViewAnalytics: true,
      canManageSettings: true
    },
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date()
  };
};

// Usuário admin em memória (fallback)
let adminUser = null;

// Inicializar usuário admin
(async () => {
  adminUser = await generateAdminUser();
})();

// Rota de login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    console.log('🔐 Tentativa de login:', { email });

    // Verificar se adminUser foi inicializado
    if (!adminUser) {
      console.log('⚠️ AdminUser ainda não foi inicializado');
      return res.status(500).json({
        error: 'Servidor ainda inicializando',
        code: 'SERVER_INITIALIZING'
      });
    }
    
    if (email !== adminUser.email) {
      console.log('❌ Email não encontrado');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    console.log('🔍 Verificando senha...');
    console.log('🔑 Senha recebida:', password);
    console.log('🔒 Hash armazenado:', adminUser.password);

    const isValidPassword = await bcrypt.compare(password, adminUser.password);
    console.log('🔐 Resultado da comparação:', isValidPassword);

    if (!isValidPassword) {
      console.log('❌ Senha incorreta');
      return res.status(401).json({
        error: 'Email ou senha incorretos',
        code: 'INVALID_CREDENTIALS'
      });
    }
    
    // Simular token JWT
    const token = 'fake-jwt-token-' + Date.now();
    
    console.log('✅ Login bem-sucedido');
    
    res.json({
      message: 'Login realizado com sucesso',
      token,
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
        permissions: adminUser.permissions,
        lastLogin: adminUser.lastLogin
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no login:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota de health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Servidor funcionando' });
});

// Rota para obter usuário atual
app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  res.json({
    user: {
      id: adminUser.id,
      name: adminUser.name,
      email: adminUser.email,
      role: adminUser.role,
      permissions: adminUser.permissions,
      lastLogin: adminUser.lastLogin,
      createdAt: adminUser.createdAt
    }
  });
});

// Rota de analytics do dashboard (dados reais do MongoDB)
app.get('/api/analytics/dashboard', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  try {
    // Buscar dados reais do banco
    const totalContacts = await Contact.countDocuments({ isActive: true });
    const highRiskContacts = await Contact.countDocuments({ isActive: true, riskLevel: 'high' });
    const firstTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'first_trimester' });
    const secondTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'second_trimester' });
    const thirdTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'third_trimester' });
    const postpartum = await Contact.countDocuments({ isActive: true, pregnancyStage: 'postpartum' });

    // Dados reais do dashboard
    const realData = {
      period: {
        type: 'month',
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate: new Date()
      },
      contacts: {
        total: totalContacts,
        highRisk: highRiskContacts,
        urgent: 0, // Pode ser calculado baseado em critérios específicos
        firstTrimester: firstTrimester,
        secondTrimester: secondTrimester,
        thirdTrimester: thirdTrimester,
        postpartum: postpartum,
        dueSoon: 0, // Pode ser calculado baseado na data de parto
        overdue: 0
      },
      messages: {
        total: 4, // Número fixo por enquanto, pode ser calculado
        fromMe: 2,
        fromContacts: 2,
        urgent: 0,
        negative: 0,
        positive: 4,
        unprocessed: 0
      },
      categories: [
        { _id: 'consulta', count: 2 },
        { _id: 'sintomas', count: 1 },
        { _id: 'duvidas', count: 1 },
        { _id: 'emergencia', count: 0 }
      ],
      dailyActivity: [
        { _id: { year: 2024, month: 12, day: 20 }, messages: 1, fromMe: 1, fromContacts: 0 },
        { _id: { year: 2024, month: 12, day: 21 }, messages: 1, fromMe: 0, fromContacts: 1 },
        { _id: { year: 2024, month: 12, day: 22 }, messages: 1, fromMe: 1, fromContacts: 0 },
        { _id: { year: 2024, month: 12, day: 23 }, messages: 1, fromMe: 0, fromContacts: 1 }
      ],
      topContacts: await Contact.find({ isActive: true })
        .sort({ updatedAt: -1 })
        .limit(5)
        .select('name phone pregnancyStage updatedAt')
        .lean()
        .then(contacts => contacts.map(contact => ({
          name: contact.name,
          phone: contact.phone,
          pregnancyStage: contact.pregnancyStage,
          messageCount: Math.floor(Math.random() * 20) + 5, // Simulado por enquanto
          lastMessage: contact.updatedAt
        })))
    };

    console.log('📊 Analytics do dashboard solicitado - Gestantes encontradas:', totalContacts);
    res.json(realData);
  } catch (error) {
    console.error('❌ Erro ao buscar analytics:', error);
    res.status(500).json({
      error: 'Erro ao buscar analytics',
      code: 'DATABASE_ERROR'
    });
  }
});

// Rota de gestantes (dados reais do MongoDB)
app.get('/api/contacts', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  console.log('🔍 Requisição /api/contacts recebida');
  console.log('🔑 Token recebido:', token ? `${token.substring(0, 20)}...` : 'NENHUM');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    console.log('❌ Token inválido ou ausente');
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('✅ Token válido aceito');

  try {
    const contacts = await Contact.find({ isActive: true }).sort({ createdAt: -1 });
    console.log('👥 Lista de gestantes solicitada - Encontradas:', contacts.length);
    res.json(contacts);
  } catch (error) {
    console.error('❌ Erro ao buscar gestantes:', error);
    res.status(500).json({
      error: 'Erro ao buscar gestantes',
      code: 'DATABASE_ERROR'
    });
  }
});

// Rota para criar nova gestante
app.post('/api/contacts', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  console.log('📝 Requisição POST /api/contacts recebida');
  console.log('🔑 Token recebido:', token ? `${token.substring(0, 20)}...` : 'NENHUM');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    console.log('❌ Token inválido ou ausente');
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('✅ Token válido aceito');

  try {
    const { name, phone, email, dueDate, trimester, notes } = req.body;

    console.log('📝 Dados recebidos:', { name, phone, email, dueDate, trimester });

    // Validações básicas
    if (!name || !phone) {
      return res.status(400).json({
        error: 'Nome e telefone são obrigatórios',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // Mapear trimester para pregnancyStage
    let pregnancyStage = 'first_trimester';
    if (trimester === 2) pregnancyStage = 'second_trimester';
    else if (trimester === 3) pregnancyStage = 'third_trimester';

    // Criar nova gestante usando mongoose
    const newContact = new Contact({
      name,
      phone,
      email: email || '',
      dueDate: dueDate ? new Date(dueDate) : null,
      pregnancyStage,
      observations: notes || '',
      isActive: true
    });

    const savedContact = await newContact.save();

    console.log('✅ Gestante criada com sucesso:', savedContact._id);

    res.status(201).json(savedContact);
  } catch (error) {
    console.error('❌ Erro ao criar gestante:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota para atualizar gestante
app.put('/api/contacts/:id', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  console.log('📝 Requisição PUT /api/contacts recebida');
  console.log('🔑 Token recebido:', token ? `${token.substring(0, 20)}...` : 'NENHUM');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    console.log('❌ Token inválido ou ausente');
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('✅ Token válido aceito');

  try {
    const { id } = req.params;
    const { name, phone, email, dueDate, trimester, notes } = req.body;

    console.log('📝 Atualizando gestante:', id);
    console.log('📝 Dados recebidos:', { name, phone, email, dueDate, trimester });

    // Validações básicas
    if (!name || !phone) {
      return res.status(400).json({
        error: 'Nome e telefone são obrigatórios',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // Mapear trimester para pregnancyStage
    let pregnancyStage = 'first_trimester';
    if (trimester === 2) pregnancyStage = 'second_trimester';
    else if (trimester === 3) pregnancyStage = 'third_trimester';

    // Atualizar gestante usando mongoose
    const updateData = {
      name,
      phone,
      email: email || '',
      dueDate: dueDate ? new Date(dueDate) : null,
      pregnancyStage,
      observations: notes || '',
      updatedAt: new Date()
    };

    const updatedContact = await Contact.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedContact) {
      return res.status(404).json({
        error: 'Gestante não encontrada',
        code: 'NOT_FOUND'
      });
    }

    console.log('✅ Gestante atualizada com sucesso:', id);

    res.json(updatedContact);
  } catch (error) {
    console.error('❌ Erro ao atualizar gestante:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota para deletar gestante
app.delete('/api/contacts/:id', async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  console.log('🗑️ Requisição DELETE /api/contacts recebida');
  console.log('🔑 Token recebido:', token ? `${token.substring(0, 20)}...` : 'NENHUM');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    console.log('❌ Token inválido ou ausente');
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('✅ Token válido aceito');

  try {
    const { id } = req.params;

    console.log('🗑️ Deletando gestante:', id);

    // Soft delete - marcar como inativa em vez de deletar
    const deletedContact = await Contact.findByIdAndUpdate(
      id,
      { isActive: false, updatedAt: new Date() },
      { new: true }
    );

    if (!deletedContact) {
      return res.status(404).json({
        error: 'Gestante não encontrada',
        code: 'NOT_FOUND'
      });
    }

    console.log('✅ Gestante deletada com sucesso:', id);

    res.json({ message: 'Gestante deletada com sucesso' });
  } catch (error) {
    console.error('❌ Erro ao deletar gestante:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Rota de WhatsApp status (dados mock)
app.get('/api/whatsapp/status', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  console.log('📱 Status do WhatsApp solicitado');
  res.json({
    status: 'connected',
    qrCode: null
  });
});

// Rota de mensagens (dados mock)
app.get('/api/messages/:pregnantWomanId', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const mockMessages = [
    {
      id: '1',
      sender: 'gestante',
      text: 'Olá! Como está minha gravidez?',
      timestamp: new Date().toISOString(),
      status: 'read'
    },
    {
      id: '2',
      sender: 'user',
      text: 'Olá! Tudo está bem. Como você está se sentindo?',
      timestamp: new Date().toISOString(),
      status: 'sent'
    }
  ];

  console.log('💬 Mensagens solicitadas para gestante:', req.params.pregnantWomanId);
  res.json(mockMessages);
});

// Rota para enviar mensagem (dados mock)
app.post('/api/messages/send', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token || (!token.startsWith('fake-jwt-token-') && !token.startsWith('eyJ'))) {
    return res.status(401).json({
      error: 'Token inválido',
      code: 'INVALID_TOKEN'
    });
  }

  const { pregnantWomanId, text } = req.body;

  const newMessage = {
    id: Date.now().toString(),
    sender: 'user',
    text: text,
    timestamp: new Date().toISOString(),
    status: 'sent'
  };

  console.log('📤 Mensagem enviada para gestante:', pregnantWomanId, 'Texto:', text);
  res.json(newMessage);
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('🚀 Servidor simples iniciado na porta', PORT);
  console.log('✅ CORS configurado para múltiplas portas');
  console.log('🔐 Usuário admin disponível:');
  console.log('   Email: <EMAIL>');
  console.log('   Senha: Admin123!@#');
});

module.exports = app;
