# Configurações do Servidor
PORT=3000
NODE_ENV=development

# MongoDB Atlas (descomente quando IP estiver liberado)
MONGODB_URI=mongodb+srv://italocabral95:<EMAIL>/gestacao-materna

# MongoDB Local (para desenvolvimento - descomente se preferir usar local)
# MONGODB_URI=mongodb://localhost:27017/gestacao-materna
# WhatsApp Web.js
WHATSAPP_SESSION_PATH=./sessions

# Gemini AI (substitua pela sua chave API)
GEMINI_API_KEY=AIzaSyAchEIEhbmB8mrF3DUZBWU56Tz1Vb6GYCA

# Frontend URL
FRONTEND_URL=http://localhost:5173