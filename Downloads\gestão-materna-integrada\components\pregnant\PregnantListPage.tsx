import React, { useState, useEffect, useCallback, useRef } from 'react'; // Added useRef to import
import PageTitle from '../shared/PageTitle';
import Button from '../shared/Button';
import PregnantTable from './PregnantTable';
import PregnantFormModal from './PregnantFormModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import { PregnantWoman } from '../../types';
import { MOCK_PREGNANT_WOMEN, ICONS } from '../../constants';
import { normalizeBrazilianPhoneNumber } from '../../utils/phoneUtils';
import Input from '../shared/Input';
import { parsePregnantWomenCSV } from '../../utils/csvParser';
import Spinner from '../shared/Spinner';
import { apiService, type Contact } from '../../src/services/api';
import { toast } from 'react-toastify';

// Mock service functions (would be in a service file)
const fetchPregnantWomen = async (): Promise<PregnantWoman[]> => {
  return new Promise(resolve => setTimeout(() => resolve([...MOCK_PREGNANT_WOMEN]), 500));
};

const addPregnantWomanAPI = async (woman: Omit<PregnantWoman, 'id' | 'createdAt'>): Promise<PregnantWoman> => {
  return new Promise(resolve => {
    setTimeout(() => {
      const newWoman: PregnantWoman = {
        ...woman,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
        phone: normalizeBrazilianPhoneNumber(woman.phone, true)
      };
      MOCK_PREGNANT_WOMEN.push(newWoman); // Note: This mutates the mock constant. In a real app, state would be managed properly.
      resolve(newWoman);
    }, 300);
  });
};

const updatePregnantWomanAPI = async (woman: PregnantWoman): Promise<PregnantWoman> => {
   return new Promise(resolve => {
    setTimeout(() => {
      const index = MOCK_PREGNANT_WOMEN.findIndex(w => w.id === woman.id);
      if (index !== -1) {
        MOCK_PREGNANT_WOMEN[index] = { ...woman, phone: normalizeBrazilianPhoneNumber(woman.phone, true) };
        resolve(MOCK_PREGNANT_WOMEN[index]);
      } else {
        // Handle error: woman not found
        resolve(woman); // Or throw error
      }
    }, 300);
  });
};

const deletePregnantWomanAPI = async (id: string): Promise<void> => {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = MOCK_PREGNANT_WOMEN.findIndex(w => w.id === id);
      if (index !== -1) {
        MOCK_PREGNANT_WOMEN.splice(index, 1);
      }
      resolve();
    }, 300);
  });
};


const PregnantListPage: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [processing, setProcessing] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null); // Changed React.useRef to useRef

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      console.log('🔍 Carregando gestantes...');
      const data = await apiService.getContacts();
      console.log('📊 Dados recebidos da API:', data);
      console.log('📊 Tipo dos dados:', typeof data);
      console.log('📊 É array?', Array.isArray(data));

      // Garantir que data seja sempre um array
      const safeData = Array.isArray(data) ? data : [];
      console.log('📊 Dados seguros:', safeData);
      console.log('📊 Quantidade de gestantes:', safeData.length);

      setContacts(safeData);
      setFilteredContacts(safeData);

      console.log('✅ Gestantes carregadas com sucesso');
    } catch (error) {
      toast.error('Erro ao carregar contatos');
      console.error('❌ Erro ao carregar contatos:', error);
      // Em caso de erro, definir arrays vazios
      setContacts([]);
      setFilteredContacts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = contacts.filter(
      (contact) =>
        contact.name.toLowerCase().includes(lowerSearchTerm) ||
        contact.phone.includes(lowerSearchTerm) ||
        (contact.email && contact.email.toLowerCase().includes(lowerSearchTerm))
    );
    setFilteredContacts(filtered);
  }, [searchTerm, contacts]);

  const handleOpenFormModal = (contact?: Contact) => {
    setSelectedContact(contact || null);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = () => {
    setSelectedContact(null);
    setIsFormModalOpen(false);
  };

  const handleOpenDeleteModal = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedContact(null);
    setIsDeleteModalOpen(false);
  };

  const handleSaveContact = async (contactData: Partial<Contact>) => {
    try {
      if (selectedContact) {
        // Atualizar contato existente
        await apiService.updateContact(selectedContact._id, contactData);
        toast.success('Contato atualizado com sucesso!');
      } else {
        // Criar novo contato
        await apiService.createContact(contactData);
        toast.success('Contato criado com sucesso!');
      }
      handleCloseFormModal();
      loadContacts();
    } catch (error) {
      toast.error('Erro ao salvar contato');
      console.error('Erro ao salvar contato:', error);
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact) return;

    try {
      await apiService.deleteContact(selectedContact._id);
      toast.success('Contato excluído com sucesso!');
      handleCloseDeleteModal();
      loadContacts();
    } catch (error) {
      toast.error('Erro ao excluir contato');
      console.error('Erro ao excluir contato:', error);
    }
  };

  const handleSendMessage = async (contact: Contact, message: string) => {
    try {
      await apiService.sendMessage(contact.phone, message);
      toast.success('Mensagem enviada com sucesso!');
    } catch (error) {
      toast.error('Erro ao enviar mensagem');
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleGenerateFollowUp = async (contact: Contact) => {
    try {
      const { message } = await apiService.generateFollowUp(contact._id);
      await handleSendMessage(contact, message);
    } catch (error) {
      toast.error('Erro ao gerar mensagem de acompanhamento');
      console.error('Erro ao gerar mensagem de acompanhamento:', error);
    }
  };

  const handleImportCSV = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProcessing(true);
      const reader = new FileReader();
      reader.onload = async (e) => {
        const csvString = e.target?.result as string;
        const parsedData = parsePregnantWomenCSV(csvString);
        // In a real app, you'd validate and then batch add these
        for (const item of parsedData) {
            if(item.name && item.dueDate && item.phone) { // Basic validation
                 await addPregnantWomanAPI({
                    name: item.name,
                    dueDate: item.dueDate, // Needs proper date parsing/validation
                    phone: item.phone,
                    email: item.email,
                    observations: item.observations
                });
            }
        }
        await loadContacts();
        setProcessing(false);
        alert(`${parsedData.length} registros processados do CSV (mock). Verifique o console para mais detalhes.`);
      };
      reader.readAsText(file);
      if(fileInputRef.current) fileInputRef.current.value = ""; // Reset file input
    }
  };


  if (loading) {
    return <div className="flex justify-center items-center h-full"><Spinner size="lg" /></div>;
  }

  return (
    <div>
      <PageTitle title="Gerenciamento de Gestantes" subtitle="Cadastre, edite e visualize informações das gestantes.">
        <div className="flex space-x-2">
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".csv" className="hidden" />
          <Button onClick={handleImportCSV} variant="outline" disabled={processing}>
            {ICONS.upload} Importar CSV
          </Button>
          <Button onClick={() => handleOpenFormModal()} disabled={processing}>
            {ICONS.add} Nova Gestante
          </Button>
        </div>
      </PageTitle>

      <div className="mb-4">
        <Input 
            placeholder="Buscar por nome, telefone, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            Icon={() => ICONS.search}
        />
      </div>

      {processing && !loading && <div className="my-4"><Spinner /> <p className="text-center text-sm">Processando...</p></div>}

      <PregnantTable
        contacts={filteredContacts}
        onEdit={handleOpenFormModal}
        onDelete={handleOpenDeleteModal}
        onSendMessage={handleSendMessage}
        onGenerateFollowUp={handleGenerateFollowUp}
      />

      <PregnantFormModal
        isOpen={isFormModalOpen}
        onClose={handleCloseFormModal}
        onSave={handleSaveContact}
        contact={selectedContact}
      />

      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleDeleteContact}
        contactName={selectedContact?.name}
      />
    </div>
  );
};

export default PregnantListPage;
