// Serviço básico para integração com Gemini AI
export interface GroundingChunk {
  web?: {
    uri: string;
    title?: string;
  };
}

export interface GeminiResponse {
  text: string;
  groundingChunks?: GroundingChunk[];
}

// Função básica para gerar texto (mock por enquanto)
export const generateText = async (
  prompt: string, 
  systemInstruction?: string
): Promise<GeminiResponse> => {
  // Simular delay da API
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Resposta mock baseada no prompt
  let mockResponse = "Esta é uma resposta simulada da IA. ";
  
  if (prompt.toLowerCase().includes('empática')) {
    mockResponse = "Entendo como você deve estar se sentindo. É normal ter essas preocupações durante a gravidez. Você está fazendo um ótimo trabalho cuidando de si mesma e do seu bebê.";
  } else if (prompt.toLowerCase().includes('informação')) {
    mockResponse = "Com base nas informações disponíveis, recomendo que você converse com seu médico sobre essa questão. Cada gestação é única e é importante ter acompanhamento profissional.";
  } else if (prompt.toLowerCase().includes('próximo passo')) {
    mockResponse = "Como próximo passo, sugiro perguntar sobre como ela está se sentindo hoje e se tem alguma dúvida específica sobre a gestação.";
  } else {
    mockResponse = "Obrigada por compartilhar isso comigo. Como posso ajudá-la melhor hoje?";
  }
  
  return {
    text: mockResponse
  };
};

// Função para gerar texto com busca no Google (mock por enquanto)
export const generateTextWithGoogleSearch = async (
  prompt: string
): Promise<GeminiResponse> => {
  // Simular delay da API
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  return {
    text: "Esta é uma resposta simulada com busca no Google. Para informações mais atualizadas, recomendo consultar fontes médicas confiáveis.",
    groundingChunks: [
      {
        web: {
          uri: "https://www.example.com/gestacao-info",
          title: "Informações sobre Gestação - Fonte Confiável"
        }
      }
    ]
  };
};
