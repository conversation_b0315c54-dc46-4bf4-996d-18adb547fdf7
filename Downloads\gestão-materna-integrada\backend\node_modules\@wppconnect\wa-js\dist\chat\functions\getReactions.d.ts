/*!
 * Copyright 2022 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Msg<PERSON><PERSON> } from '../../whatsapp';
/**
 * Get all reactions in a message
 * @example
 * ```javascript
 * WPP.chat.getReactions('true_[number]@c.us_ABCDEF');
 * ```
 * @category Chat
 */
export declare function getReactions(msgId: string): Promise<{
    reactionByMe: {
        id: Msg<PERSON>ey;
        orphan: number;
        msgId: Msg<PERSON>ey;
        reactionText: string;
        read: boolean;
        senderUserJid: string;
        timestamp: number;
    };
    reactions: {
        aggregateEmoji: string;
        hasReactionByMe: boolean;
        senders: {
            id: <PERSON>g<PERSON><PERSON>;
            orphan: number;
            msgId: <PERSON>g<PERSON><PERSON>;
            reactionText: string;
            read: boolean;
            senderUserJid: string;
            timestamp: number;
        }[];
    }[];
}>;
