/*!
 * Copyright 2021 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Wid } from '../../whatsapp';
export interface QueryExistsResult {
    wid: Wid;
    biz: boolean;
    bizInfo?: {
        verifiedName?: {
            isApi: boolean;
            level: string;
            name: string;
            privacyMode: any;
            serial: string;
        };
    };
    disappearingMode?: {
        duration: number;
        settingTimestamp: number;
    };
    status?: string;
}
/**
 * Check if the number exists and what is correct ID
 *
 * This help to identify numbers with nine digit in Brazil
 *
 * @example
 * ```javascript
 * const result = await WPP.contact.queryExists('[number]@c.us');
 * console.log(result.wid); // Correct ID
 * ```
 *
 * @category Contact
 */
export declare function queryExists(contactId: string | Wid): Promise<QueryExistsResult | null>;
