import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import Login from './pages/Login';
import { authService } from './services/authService';
import Sidebar from '../components/shared/Sidebar';
import Navbar from '../components/shared/Navbar';
import DashboardPage from '../components/dashboard/DashboardPage';
import PregnantListPage from '../components/pregnant/PregnantListPage';
import WhatsAppPage from '../components/whatsapp/WhatsAppPage';
import SettingsPage from '../components/settings/SettingsPage';
import { DEFAULT_API_KEY_NOTICE } from '../constants';

// Componente de rota protegida
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = authService.isAuthenticated();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// Layout principal com sidebar
const MainLayout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [apiKeyStatus, setApiKeyStatus] = useState(DEFAULT_API_KEY_NOTICE);

  useEffect(() => {
    // Verificar status da API key
    const checkApiKey = () => {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (apiKey && apiKey !== 'your-gemini-api-key-here') {
        setApiKeyStatus('API Key configurada');
      } else {
        setApiKeyStatus(DEFAULT_API_KEY_NOTICE);
      }
    };

    checkApiKey();
  }, []);

  return (
    <div className="flex h-screen bg-neutral-light">
      <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} apiKeyStatus={apiKeyStatus} />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-neutral-light p-6">
          <Routes>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/gestantes" element={<PregnantListPage />} />
            <Route path="/whatsapp" element={<WhatsAppPage />} />
            <Route path="/configuracoes" element={<SettingsPage />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Rota de logout forçado */}
        <Route
          path="/logout"
          element={
            <div>
              {(() => {
                localStorage.clear();
                authService.logout();
                window.location.href = '/login';
                return null;
              })()}
            </div>
          }
        />

        {/* Rota de login */}
        <Route
          path="/login"
          element={<Login />}
        />

        {/* Rotas protegidas com layout completo */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        />

        {/* Rota raiz - sempre redirecionar para login */}
        <Route
          path="/"
          element={<Navigate to="/login" replace />}
        />
      </Routes>

      <ToastContainer position="top-right" autoClose={3000} />
    </Router>
  );
};

export default App; 