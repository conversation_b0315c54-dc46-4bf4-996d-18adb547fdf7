import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import Login from './pages/Login';
import { authService } from './services/authService';

const App: React.FC = () => {
  // Estado simples sem hook complexo
  const [isAuthenticated, setIsAuthenticated] = useState(authService.isAuthenticated());
  const [user, setUser] = useState(authService.getUser());

  // Verificar autenticação apenas uma vez
  useEffect(() => {
    // Listener para mudanças no localStorage
    const handleStorageChange = () => {
      setIsAuthenticated(authService.isAuthenticated());
      setUser(authService.getUser());
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []); // Array vazio - executa apenas uma vez

  const handleLogout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setUser(null);
    window.location.reload();
  };

  return (
    <Router>
      <Routes>
        {/* Rota de login */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
          }
        />

        {/* Rota raiz - redirecionar baseado na autenticação */}
        <Route
          path="/"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
          }
        />

        {/* Dashboard protegido */}
        <Route
          path="/dashboard"
          element={
            isAuthenticated ? (
              <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <div className="bg-white p-8 rounded-lg shadow-lg">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    🎉 Dashboard Funcionando!
                  </h1>
                  <p className="text-gray-600 mb-4">
                    Sistema de autenticação funcionando corretamente.
                  </p>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-green-800 font-medium">
                      ✅ Login realizado com sucesso!
                    </p>
                    <p className="text-green-600 text-sm">
                      Usuário: {user?.name}
                    </p>
                    <p className="text-green-600 text-sm">
                      Email: {user?.email}
                    </p>
                    <p className="text-green-600 text-sm">
                      Role: {user?.role}
                    </p>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="mt-4 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                  >
                    Logout
                  </button>
                </div>
              </div>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Rota catch-all */}
        <Route
          path="*"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
          }
        />
      </Routes>

      <ToastContainer position="top-right" autoClose={3000} />
    </Router>
  );
};

export default App; 