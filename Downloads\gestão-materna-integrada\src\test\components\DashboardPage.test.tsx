import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import DashboardPage from '../../../components/dashboard/DashboardPage';
import { apiService } from '../../services/apiService';

// Mock do apiService
vi.mock('../../services/apiService', () => ({
  apiService: {
    getDashboardMetrics: vi.fn(),
    getNewRegistrationsData: vi.fn(),
    getAgeDistributionData: vi.fn(),
  },
}));

// Mock do hook useDashboard
const mockUseDashboard = vi.fn();
const mockUseDashboardFreshness = vi.fn(() => false);

vi.mock('../../src/hooks/useDashboard', () => ({
  useDashboard: mockUseDashboard,
  useDashboardFreshness: mockUseDashboardFreshness,
}));

// Mock dos componentes de gráfico
vi.mock('../../../components/dashboard/PregnantWomenChart', () => ({
  default: ({ data }: { data: any[] }) => (
    <div data-testid="pregnant-women-chart">
      Chart with {data.length} items
    </div>
  ),
}));

vi.mock('../../../components/dashboard/MessageStatsChart', () => ({
  default: ({ data }: { data: any[] }) => (
    <div data-testid="message-stats-chart">
      Chart with {data.length} items
    </div>
  ),
}));

const mockMetrics = {
  totalPregnantWomen: 25,
  avgWeeksGestation: 28,
  messagesSentThisMonth: 150,
  activeConversations: 12,
};

const mockRegistrations = [
  { name: 'Jan', value: 5 },
  { name: 'Fev', value: 8 },
  { name: 'Mar', value: 12 },
];

const mockAgeDistribution = [
  { name: '18-25', value: 10 },
  { name: '26-30', value: 8 },
  { name: '31-35', value: 5 },
];

const renderDashboard = () => {
  return render(
    <BrowserRouter>
      <DashboardPage />
      <ToastContainer />
    </BrowserRouter>
  );
};

describe('DashboardPage', () => {
  const mockApiService = vi.mocked(apiService);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve renderizar o loading inicialmente', () => {
    // Mock do hook para retornar estado de loading
    mockUseDashboard.mockReturnValue({
      data: { metrics: null, newRegistrations: [], ageDistribution: [] },
      loading: true,
      error: null,
      refetch: vi.fn(),
      lastUpdated: null,
    });

    renderDashboard();

    expect(screen.getByText('Atualizando...')).toBeInTheDocument();
  });

  it('deve renderizar os dados do dashboard com sucesso', async () => {
    // Mock do hook com dados carregados
    mockUseDashboard.mockReturnValue({
      data: {
        metrics: mockMetrics,
        newRegistrations: mockRegistrations,
        ageDistribution: mockAgeDistribution
      },
      loading: false,
      error: null,
      refetch: vi.fn(),
      lastUpdated: new Date(),
    });

    renderDashboard();

    // Verificar título
    expect(screen.getByText('Dashboard Visão Geral')).toBeInTheDocument();

    // Verificar métricas
    expect(screen.getByText('25')).toBeInTheDocument(); // Total de gestantes
    expect(screen.getByText('28 sem.')).toBeInTheDocument(); // Média semanas
    expect(screen.getByText('150')).toBeInTheDocument(); // Mensagens enviadas
    expect(screen.getByText('12')).toBeInTheDocument(); // Conversas ativas

    // Verificar gráficos
    expect(screen.getByTestId('pregnant-women-chart')).toBeInTheDocument();
    expect(screen.getByTestId('message-stats-chart')).toBeInTheDocument();
  });

  it('deve exibir erro quando a API falha', async () => {
    // Mock do hook com erro
    const errorMessage = 'Erro ao carregar dados do dashboard. Tente novamente.';
    mockUseDashboard.mockReturnValue({
      data: { metrics: null, newRegistrations: [], ageDistribution: [] },
      loading: false,
      error: errorMessage,
      refetch: vi.fn(),
      lastUpdated: null,
    });

    renderDashboard();

    // Verificar mensagem de erro
    expect(screen.getByText(errorMessage)).toBeInTheDocument();

    // Verificar botão de tentar novamente
    expect(screen.getByText('Tentar Novamente')).toBeInTheDocument();
  });

  it('deve chamar refetch quando botão é clicado', async () => {
    const mockRefetch = vi.fn();
    mockUseDashboard.mockReturnValue({
      data: {
        metrics: mockMetrics,
        newRegistrations: mockRegistrations,
        ageDistribution: mockAgeDistribution
      },
      loading: false,
      error: null,
      refetch: mockRefetch,
      lastUpdated: new Date(),
    });

    renderDashboard();

    const refetchButton = screen.getByText('Atualizar');
    refetchButton.click();

    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it('deve exibir mensagem quando não há dados para gráficos', async () => {
    mockUseDashboard.mockReturnValue({
      data: {
        metrics: mockMetrics,
        newRegistrations: [],
        ageDistribution: []
      },
      loading: false,
      error: null,
      refetch: vi.fn(),
      lastUpdated: new Date(),
    });

    renderDashboard();

    expect(screen.getAllByText('Sem dados para exibir.')).toHaveLength(2);
  });
});
