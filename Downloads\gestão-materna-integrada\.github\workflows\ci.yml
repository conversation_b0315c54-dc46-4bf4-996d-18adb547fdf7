name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Run linting
      run: npm run lint
    
    - name: Run unit tests
      run: npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Build application
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files-${{ matrix.node-version }}
        path: dist/

  e2e-tests:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: |
          test-results/
          playwright-report/
    
    - name: Upload E2E videos
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: e2e-videos
        path: test-results/

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Run security audit
      run: npm audit --audit-level=moderate
    
    - name: Run dependency check
      run: npx audit-ci --moderate

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build for staging
      run: npm run build
      env:
        NODE_ENV: staging
    
    - name: Deploy to staging
      run: echo "Deploy to staging environment"
      # Aqui você adicionaria os comandos específicos para deploy
      # Por exemplo: rsync, scp, ou integração com serviços de cloud

  deploy-production:
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build for production
      run: npm run build
      env:
        NODE_ENV: production
    
    - name: Run production tests
      run: npm run test:prod
    
    - name: Deploy to production
      run: echo "Deploy to production environment"
      # Aqui você adicionaria os comandos específicos para deploy em produção

  quality-gate:
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, security-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download coverage reports
      uses: actions/download-artifact@v3
      with:
        name: coverage-reports
        path: coverage/
    
    - name: Quality Gate Check
      run: |
        echo "Checking quality gates..."
        
        # Verificar cobertura mínima (80%)
        COVERAGE=$(grep -o 'Lines.*[0-9]*\.[0-9]*%' coverage/lcov-report/index.html | grep -o '[0-9]*\.[0-9]*' | head -1)
        echo "Coverage: $COVERAGE%"
        
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "❌ Coverage below 80%: $COVERAGE%"
          exit 1
        else
          echo "✅ Coverage above 80%: $COVERAGE%"
        fi
        
        # Verificar se todos os testes passaram
        echo "✅ All tests passed"
        echo "✅ Security scan passed"
        echo "✅ Quality gate passed"

  notification:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production, quality-gate]
    if: always()

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Notify Slack - Success
      if: ${{ needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success' }}
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#gestao-materna'
        username: 'GitHub Actions'
        icon_emoji: ':rocket:'
        title: '🎉 Deploy Realizado com Sucesso!'
        text: |
          *Projeto:* Gestão Materna Integrada
          *Branch:* ${{ github.ref_name }}
          *Commit:* ${{ github.sha }}
          *Autor:* ${{ github.actor }}
          *Ambiente:* ${{ needs.deploy-production.result == 'success' && 'Production' || 'Staging' }}

          ✅ Todos os testes passaram
          ✅ Security scan aprovado
          ✅ Quality gates atendidos
          ✅ Deploy realizado com sucesso

          *Detalhes:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        fields: |
          [
            {
              "title": "Testes Unitários",
              "value": "${{ needs.test.outputs.test-results || 'Passou' }}",
              "short": true
            },
            {
              "title": "Testes E2E",
              "value": "${{ needs.e2e-tests.outputs.e2e-results || 'Passou' }}",
              "short": true
            },
            {
              "title": "Coverage",
              "value": "${{ needs.test.outputs.coverage || '> 80%' }}",
              "short": true
            },
            {
              "title": "Security",
              "value": "${{ needs.security-scan.outputs.vulnerabilities || 'Nenhuma' }}",
              "short": true
            }
          ]
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Discord - Success
      if: ${{ needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success' }}
      uses: Ilshidur/action-discord@master
      with:
        args: |
          🎉 **Deploy Realizado com Sucesso!**

          **Projeto:** Gestão Materna Integrada
          **Branch:** ${{ github.ref_name }}
          **Ambiente:** ${{ needs.deploy-production.result == 'success' && 'Production' || 'Staging' }}
          **Autor:** ${{ github.actor }}

          ✅ Todos os testes passaram
          ✅ Security scan aprovado
          ✅ Quality gates atendidos

          [Ver detalhes](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}

    - name: Notify Slack - Failure
      if: ${{ needs.test.result == 'failure' || needs.e2e-tests.result == 'failure' || needs.security-scan.result == 'failure' }}
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#gestao-materna'
        username: 'GitHub Actions'
        icon_emoji: ':x:'
        title: '❌ Pipeline Falhou!'
        text: |
          *Projeto:* Gestão Materna Integrada
          *Branch:* ${{ github.ref_name }}
          *Commit:* ${{ github.sha }}
          *Autor:* ${{ github.actor }}

          ❌ Falhas detectadas no pipeline:
          ${{ needs.test.result == 'failure' && '• Testes unitários falharam' || '' }}
          ${{ needs.e2e-tests.result == 'failure' && '• Testes E2E falharam' || '' }}
          ${{ needs.security-scan.result == 'failure' && '• Security scan falhou' || '' }}

          *Ação necessária:* Verificar logs e corrigir problemas
          *Detalhes:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        fields: |
          [
            {
              "title": "Testes Unitários",
              "value": "${{ needs.test.result }}",
              "short": true
            },
            {
              "title": "Testes E2E",
              "value": "${{ needs.e2e-tests.result }}",
              "short": true
            },
            {
              "title": "Security Scan",
              "value": "${{ needs.security-scan.result }}",
              "short": true
            },
            {
              "title": "Quality Gate",
              "value": "${{ needs.quality-gate.result }}",
              "short": true
            }
          ]
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Discord - Failure
      if: ${{ needs.test.result == 'failure' || needs.e2e-tests.result == 'failure' || needs.security-scan.result == 'failure' }}
      uses: Ilshidur/action-discord@master
      with:
        args: |
          ❌ **Pipeline Falhou!**

          **Projeto:** Gestão Materna Integrada
          **Branch:** ${{ github.ref_name }}
          **Autor:** ${{ github.actor }}

          **Falhas detectadas:**
          ${{ needs.test.result == 'failure' && '• Testes unitários falharam' || '' }}
          ${{ needs.e2e-tests.result == 'failure' && '• Testes E2E falharam' || '' }}
          ${{ needs.security-scan.result == 'failure' && '• Security scan falhou' || '' }}

          **Ação necessária:** Verificar logs e corrigir problemas

          [Ver detalhes](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}

    - name: Notify Teams - Success
      if: ${{ needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success' }}
      uses: skitionek/notify-microsoft-teams@master
      with:
        webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
        overwrite: |
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "28a745",
            "summary": "Deploy realizado com sucesso",
            "sections": [{
              "activityTitle": "🎉 Deploy Realizado com Sucesso!",
              "activitySubtitle": "Gestão Materna Integrada",
              "activityImage": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "facts": [{
                "name": "Projeto",
                "value": "Gestão Materna Integrada"
              }, {
                "name": "Branch",
                "value": "${{ github.ref_name }}"
              }, {
                "name": "Ambiente",
                "value": "${{ needs.deploy-production.result == 'success' && 'Production' || 'Staging' }}"
              }, {
                "name": "Autor",
                "value": "${{ github.actor }}"
              }],
              "markdown": true
            }],
            "potentialAction": [{
              "@type": "OpenUri",
              "name": "Ver Detalhes",
              "targets": [{
                "os": "default",
                "uri": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
              }]
            }]
          }

    - name: Notify Teams - Failure
      if: ${{ needs.test.result == 'failure' || needs.e2e-tests.result == 'failure' || needs.security-scan.result == 'failure' }}
      uses: skitionek/notify-microsoft-teams@master
      with:
        webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
        overwrite: |
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "dc3545",
            "summary": "Pipeline falhou",
            "sections": [{
              "activityTitle": "❌ Pipeline Falhou!",
              "activitySubtitle": "Gestão Materna Integrada",
              "activityImage": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "facts": [{
                "name": "Projeto",
                "value": "Gestão Materna Integrada"
              }, {
                "name": "Branch",
                "value": "${{ github.ref_name }}"
              }, {
                "name": "Autor",
                "value": "${{ github.actor }}"
              }, {
                "name": "Testes Unitários",
                "value": "${{ needs.test.result }}"
              }, {
                "name": "Testes E2E",
                "value": "${{ needs.e2e-tests.result }}"
              }, {
                "name": "Security Scan",
                "value": "${{ needs.security-scan.result }}"
              }],
              "markdown": true
            }],
            "potentialAction": [{
              "@type": "OpenUri",
              "name": "Ver Detalhes",
              "targets": [{
                "os": "default",
                "uri": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
              }]
            }]
          }
