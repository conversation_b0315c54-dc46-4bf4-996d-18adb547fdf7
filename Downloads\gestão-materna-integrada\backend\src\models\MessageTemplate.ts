import mongoose, { Schema, Document } from 'mongoose';

export interface IMessageTemplate extends Document {
  name: string;
  category: 'routine_checkup' | 'milestone' | 'educational' | 'emotional_support' | 'reminder' | 'emergency_follow_up';
  
  // Conteúdo do template
  title: string;
  content: string;
  variables: string[]; // Lista de variáveis disponíveis como {{name}}, {{week}}, etc.
  
  // Condições de uso
  gestationalWeekMin?: number;
  gestationalWeekMax?: number;
  pregnancyStage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  isHighRisk?: boolean;
  
  // Configurações
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requiresResponse: boolean;
  followUpDays?: number; // Dias para follow-up se não houver resposta
  
  // Metadados
  isActive: boolean;
  usageCount: number;
  successRate: number; // Taxa de resposta
  
  // Dados do criador
  createdBy: mongoose.Types.ObjectId;
  lastModifiedBy?: mongoose.Types.ObjectId;
  
  // Métodos
  render(variables: Map<string, string>): string;
  incrementUsage(): Promise<void>;
  updateSuccessRate(responded: boolean): Promise<void>;
}

const MessageTemplateSchema = new Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
  },
  category: {
    type: String,
    enum: ['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up'],
    required: true,
    index: true
  },
  
  // Conteúdo do template
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Título deve ter no máximo 200 caracteres']
  },
  content: {
    type: String,
    required: true,
    maxlength: [2000, 'Conteúdo deve ter no máximo 2000 caracteres']
  },
  variables: [{
    type: String,
    trim: true
  }],
  
  // Condições de uso
  gestationalWeekMin: {
    type: Number,
    min: 0,
    max: 45
  },
  gestationalWeekMax: {
    type: Number,
    min: 0,
    max: 45
  },
  pregnancyStage: {
    type: String,
    enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
  },
  isHighRisk: Boolean,
  
  // Configurações
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  requiresResponse: {
    type: Boolean,
    default: false
  },
  followUpDays: {
    type: Number,
    min: 1,
    max: 30
  },
  
  // Metadados
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  usageCount: {
    type: Number,
    default: 0
  },
  successRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // Dados do criador
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Índices para performance
MessageTemplateSchema.index({ category: 1, isActive: 1 });
MessageTemplateSchema.index({ pregnancyStage: 1, isActive: 1 });
MessageTemplateSchema.index({ createdBy: 1 });

// Método para renderizar template com variáveis
MessageTemplateSchema.methods.render = function(variables: Map<string, string>): string {
  let renderedContent = this.content;
  
  // Substituir variáveis no conteúdo
  for (const [key, value] of variables) {
    const placeholder = new RegExp(`{{${key}}}`, 'g');
    renderedContent = renderedContent.replace(placeholder, value);
  }
  
  return renderedContent;
};

// Método para incrementar uso
MessageTemplateSchema.methods.incrementUsage = async function(): Promise<void> {
  this.usageCount += 1;
  await this.save();
};

// Método para atualizar taxa de sucesso
MessageTemplateSchema.methods.updateSuccessRate = async function(responded: boolean): Promise<void> {
  const totalResponses = this.usageCount;
  const currentSuccessCount = Math.round((this.successRate / 100) * totalResponses);
  
  const newSuccessCount = responded ? currentSuccessCount + 1 : currentSuccessCount;
  this.successRate = totalResponses > 0 ? (newSuccessCount / totalResponses) * 100 : 0;
  
  await this.save();
};

export const MessageTemplate = mongoose.model<IMessageTemplate>('MessageTemplate', MessageTemplateSchema);
