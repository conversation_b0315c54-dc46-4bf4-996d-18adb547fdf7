{"config": {"configFile": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Users/<USER>/Downloads/gestão-materna-integrada/src/test/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "crud.e2e.test.ts", "file": "crud.e2e.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Testes E2E - CRUD de Gestantes", "file": "crud.e2e.test.ts", "line": 6, "column": 6, "specs": [{"title": "deve navegar para página de gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30269, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 17}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"menu-gestantes\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m   test(\u001b[32m'deve navegar para página de gestantes'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 16 |\u001b[39m     \u001b[90m// Clicar no menu de gestantes\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"menu-gestantes\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Verificar se chegou na página correta\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m`${BASE_URL}/gestantes`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:17:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.084Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-5d1a50d94e93232fd15b", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 30172, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 28}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 26 |\u001b[39m     \n \u001b[90m 27 |\u001b[39m     \u001b[90m// Aguardar a tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 28 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Verificar se há gestantes na lista\u001b[39m\n \u001b[90m 31 |\u001b[39m     \u001b[36mconst\u001b[39m rows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:28:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.086Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-92fe2c14e21482a8b5ba", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 30191, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 39}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 37 |\u001b[39m     \n \u001b[90m 38 |\u001b[39m     \u001b[90m// Clicar no botão \"Nova Gestante\"\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m     \n \u001b[90m 41 |\u001b[39m     \u001b[90m// Aguardar modal abrir\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:39:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.083Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-a2280cebf680f7d3ae66", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 30185, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 63}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \n \u001b[90m 62 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Clicar no primeiro botão de editar\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"pregnant-table\"] button:has-text(\"Editar\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:63:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.091Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-ef5809a9a99353748f5b", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30190, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 92}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 90 |\u001b[39m     \n \u001b[90m 91 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m     \n \u001b[90m 94 |\u001b[39m     \u001b[90m// Obter nome da primeira gestante\u001b[39m\n \u001b[90m 95 |\u001b[39m     \u001b[36mconst\u001b[39m firstRowName \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr:first-child td:first-child'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:92:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.083Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-1d9e1610567aae373baf", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "timedOut", "duration": 30174, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 120}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 118 |\u001b[39m     \n \u001b[90m 119 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 120 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 121 |\u001b[39m     \n \u001b[90m 122 |\u001b[39m     \u001b[90m// Obter total de linhas inicial\u001b[39m\n \u001b[90m 123 |\u001b[39m     \u001b[36mconst\u001b[39m initialRows \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:120:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:16:56.084Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-651464c767d84102a56f", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 4, "status": "timedOut", "duration": 30773, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 146}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 144 |\u001b[39m     \n \u001b[90m 145 |\u001b[39m     \u001b[90m// Abrir modal de criação\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m     \n \u001b[90m 149 |\u001b[39m     \u001b[90m// Tentar submeter sem preencher campos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:146:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.543Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-5f587022e0be6fc36e05", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 3, "status": "timedOut", "duration": 30991, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 164}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 162 |\u001b[39m     \n \u001b[90m 163 |\u001b[39m     \u001b[90m// Abrir modal\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 164 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 165 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 166 |\u001b[39m     \n \u001b[90m 167 |\u001b[39m     \u001b[90m// Preencher alguns campos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:164:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.574Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-88475b78059ee0fa8f69", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "timedOut", "duration": 30950, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 184}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 182 |\u001b[39m     \n \u001b[90m 183 |\u001b[39m     \u001b[90m// Abrir modal\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 186 |\u001b[39m     \n \u001b[90m 187 |\u001b[39m     \u001b[90m// Preencher com dados inválidos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:184:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.564Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-89c72f92dbbf0f095e74", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 5, "status": "failed", "duration": 12211, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:210:38", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}, "snippet": "\u001b[0m \u001b[90m 208 |\u001b[39m     \n \u001b[90m 209 |\u001b[39m     \u001b[90m// Verificar se página é responsiva\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 210 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 211 |\u001b[39m     \n \u001b[90m 212 |\u001b[39m     \u001b[90m// Verificar se tabela é scrollável horizontalmente\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 208 |\u001b[39m     \n \u001b[90m 209 |\u001b[39m     \u001b[90m// Verificar se página é responsiva\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 210 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 211 |\u001b[39m     \n \u001b[90m 212 |\u001b[39m     \u001b[90m// Verificar se tabela é scrollável horizontalmente\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:210:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.567Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-d4806374647262aa641f", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "timedOut", "duration": 30925, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 229}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 227 |\u001b[39m     \n \u001b[90m 228 |\u001b[39m     \u001b[90m// Fazer uma busca\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 229 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Ana'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 230 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 231 |\u001b[39m     \n \u001b[90m 232 |\u001b[39m     \u001b[90m// Navegar para outra página\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:229:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.622Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-97e01d960b01413131b8", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "timedOut", "duration": 30150, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 17}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"menu-gestantes\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m   test(\u001b[32m'deve navegar para página de gestantes'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 16 |\u001b[39m     \u001b[90m// Clicar no menu de gestantes\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"menu-gestantes\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Verificar se chegou na página correta\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m`${BASE_URL}/gestantes`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:17:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:29.641Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-fe5d80cf493a3f2bbf32", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 5, "status": "timedOut", "duration": 30093, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 28}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 26 |\u001b[39m     \n \u001b[90m 27 |\u001b[39m     \u001b[90m// Aguardar a tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 28 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Verificar se há gestantes na lista\u001b[39m\n \u001b[90m 31 |\u001b[39m     \u001b[36mconst\u001b[39m rows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:28:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:17:43.227Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-bcba05d7869279b5e994", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 4, "status": "timedOut", "duration": 30077, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 39}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 37 |\u001b[39m     \n \u001b[90m 38 |\u001b[39m     \u001b[90m// Clicar no botão \"Nova Gestante\"\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m     \n \u001b[90m 41 |\u001b[39m     \u001b[90m// Aguardar modal abrir\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:39:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:02.015Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-8e6c46da28ad60cba86b", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "timedOut", "duration": 30079, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 63}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \n \u001b[90m 62 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Clicar no primeiro botão de editar\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"pregnant-table\"] button:has-text(\"Editar\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:63:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:02.186Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-e3090d81c9d68eba06d4", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "timedOut", "duration": 30092, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 92}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 90 |\u001b[39m     \n \u001b[90m 91 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m     \n \u001b[90m 94 |\u001b[39m     \u001b[90m// Obter nome da primeira gestante\u001b[39m\n \u001b[90m 95 |\u001b[39m     \u001b[36mconst\u001b[39m firstRowName \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr:first-child td:first-child'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:92:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:02.261Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-47c4f2914a7689f1736e", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "timedOut", "duration": 30080, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 120}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 118 |\u001b[39m     \n \u001b[90m 119 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 120 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 121 |\u001b[39m     \n \u001b[90m 122 |\u001b[39m     \u001b[90m// Obter total de linhas inicial\u001b[39m\n \u001b[90m 123 |\u001b[39m     \u001b[36mconst\u001b[39m initialRows \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:120:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:02.477Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-db531ee35fce16d99da9", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 0, "status": "timedOut", "duration": 30104, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 146}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 144 |\u001b[39m     \n \u001b[90m 145 |\u001b[39m     \u001b[90m// Abrir modal de criação\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 148 |\u001b[39m     \n \u001b[90m 149 |\u001b[39m     \u001b[90m// Tentar submeter sem preencher campos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:146:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:02.737Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-d3aa50dd4ee517324fcc", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 18, "parallelIndex": 5, "status": "timedOut", "duration": 30093, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 164}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 162 |\u001b[39m     \n \u001b[90m 163 |\u001b[39m     \u001b[90m// Abrir modal\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 164 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 165 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 166 |\u001b[39m     \n \u001b[90m 167 |\u001b[39m     \u001b[90m// Preencher alguns campos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:164:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:15.336Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\\video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-c87d23d339349d690af6", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 19, "parallelIndex": 4, "status": "interrupted", "duration": 25309, "error": {"message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n", "stack": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:184:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 184}, "snippet": "\u001b[0m \u001b[90m 182 |\u001b[39m     \n \u001b[90m 183 |\u001b[39m     \u001b[90m// Abrir modal\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 186 |\u001b[39m     \n \u001b[90m 187 |\u001b[39m     \u001b[90m// Preencher com dados inválidos\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 184}, "message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 182 |\u001b[39m     \n \u001b[90m 183 |\u001b[39m     \u001b[90m// Abrir modal\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 184 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 186 |\u001b[39m     \n \u001b[90m 187 |\u001b[39m     \u001b[90m// Preencher com dados inválidos\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:184:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:34.253Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-firefox\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 184}}], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-ab2efbafe84ebc69fdb6", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "failed", "duration": 15263, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:210:38", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}, "snippet": "\u001b[0m \u001b[90m 208 |\u001b[39m     \n \u001b[90m 209 |\u001b[39m     \u001b[90m// Verificar se página é responsiva\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 210 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 211 |\u001b[39m     \n \u001b[90m 212 |\u001b[39m     \u001b[90m// Verificar se tabela é scrollável horizontalmente\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('h1')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 208 |\u001b[39m     \n \u001b[90m 209 |\u001b[39m     \u001b[90m// Verificar se página é responsiva\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 210 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 211 |\u001b[39m     \n \u001b[90m 212 |\u001b[39m     \u001b[90m// Verificar se tabela é scrollável horizontalmente\u001b[39m\n \u001b[90m 213 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:210:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:34.830Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 38, "line": 210}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-890b24a081ced9d1ffd7", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "interrupted", "duration": 23814, "error": {"message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:229:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 229}, "snippet": "\u001b[0m \u001b[90m 227 |\u001b[39m     \n \u001b[90m 228 |\u001b[39m     \u001b[90m// Fazer uma busca\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 229 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Ana'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 230 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 231 |\u001b[39m     \n \u001b[90m 232 |\u001b[39m     \u001b[90m// Navegar para outra página\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 229}, "message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 227 |\u001b[39m     \n \u001b[90m 228 |\u001b[39m     \u001b[90m// Fazer uma busca\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 229 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Ana'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 230 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 231 |\u001b[39m     \n \u001b[90m 232 |\u001b[39m     \u001b[90m// Navegar para outra página\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:229:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:35.591Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-firefox\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 229}}], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-bbd33f680ac1fb94a57b", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "failed", "duration": 968, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-22\\0b824a5ac4cf6a8f45452175437a1298.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-22\\0b824a5ac4cf6a8f45452175437a1298.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-22\\0b824a5ac4cf6a8f45452175437a1298.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:36.700Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-fd206c48bfa846c0d9f5", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 0, "status": "failed", "duration": 524, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-23\\0057dcdb10f5841d50cdf3ede92537cd.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-23\\0057dcdb10f5841d50cdf3ede92537cd.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-23\\0057dcdb10f5841d50cdf3ede92537cd.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:37.509Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-de77cc2140c387055f90", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 24, "parallelIndex": 2, "status": "failed", "duration": 657, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-24\\bb96dca59228228612eab6f95fd85e90.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-24\\bb96dca59228228612eab6f95fd85e90.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-24\\bb96dca59228228612eab6f95fd85e90.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:39.252Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-8b7fed645b62d5c276ec", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 25, "parallelIndex": 0, "status": "failed", "duration": 712, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-25\\984738d79a61c187d4454c9824a9285b.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-25\\984738d79a61c187d4454c9824a9285b.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-25\\984738d79a61c187d4454c9824a9285b.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:39.340Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-6e873318cb0c2a9629a8", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 26, "parallelIndex": 2, "status": "failed", "duration": 524, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-26\\f6325d2b115bbab50b8f70a103764c7e.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-26\\f6325d2b115bbab50b8f70a103764c7e.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-26\\f6325d2b115bbab50b8f70a103764c7e.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:41.249Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-43c8f192c9ea2da0eb62", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 27, "parallelIndex": 0, "status": "failed", "duration": 503, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-27\\d75363f6503d7508e73782682d145ae2.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-27\\d75363f6503d7508e73782682d145ae2.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-27\\d75363f6503d7508e73782682d145ae2.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:41.368Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-e5e2644bb0d396d20d72", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "failed", "duration": 496, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-28\\fe100f9683af4e7da584780d8e4fe64a.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-28\\fe100f9683af4e7da584780d8e4fe64a.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-28\\fe100f9683af4e7da584780d8e4fe64a.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:43.048Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-fc1d5c76c0c0410e50c1", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 0, "status": "failed", "duration": 521, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-29\\1fac47871ae2a54b1752b9b140cad92a.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-29\\1fac47871ae2a54b1752b9b140cad92a.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-29\\1fac47871ae2a54b1752b9b140cad92a.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:43.152Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-c62692789eb8da5de380", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 30, "parallelIndex": 2, "status": "failed", "duration": 600, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-30\\55951b5fd47e9481706e7c5a4feccb6a.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-30\\55951b5fd47e9481706e7c5a4feccb6a.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-30\\55951b5fd47e9481706e7c5a4feccb6a.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:44.742Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-972615f074ac94ebb749", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 31, "parallelIndex": 0, "status": "failed", "duration": 524, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-31\\aa75d45cc5fc363608fc1ed9075b0abd.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-31\\aa75d45cc5fc363608fc1ed9075b0abd.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-31\\aa75d45cc5fc363608fc1ed9075b0abd.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:44.998Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-ac4be846599a6f59faf4", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 2, "status": "failed", "duration": 502, "error": {"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-32\\cce51c186aebb183f25e13f448e7077c.webm' for writing: No such file or directory", "stack": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-32\\cce51c186aebb183f25e13f448e7077c.webm' for writing: No such file or directory"}, "errors": [{"message": "Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\.playwright-artifacts-32\\cce51c186aebb183f25e13f448e7077c.webm' for writing: No such file or directory"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:46.681Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-webkit\\test-failed-1.png"}]}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-1328f4c9ef537416d92e", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 33, "parallelIndex": 0, "status": "failed", "duration": 13149, "error": {"message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"menu-gestantes\"]')\u001b[22m\n", "stack": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"menu-gestantes\"]')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:17:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m   test(\u001b[32m'deve navegar para página de gestantes'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 16 |\u001b[39m     \u001b[90m// Clicar no menu de gestantes\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"menu-gestantes\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Verificar se chegou na página correta\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m`${BASE_URL}/gestantes`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 17}, "message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"menu-gestantes\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m   test(\u001b[32m'deve navegar para página de gestantes'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 16 |\u001b[39m     \u001b[90m// Clicar no menu de gestantes\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"menu-gestantes\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Verificar se chegou na página correta\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m`${BASE_URL}/gestantes`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:17:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:46.797Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 17}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-169f45f3dfe5c97b06e0", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 34, "parallelIndex": 5, "status": "failed", "duration": 12361, "error": {"message": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n", "stack": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:28:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 28}, "snippet": "\u001b[0m \u001b[90m 26 |\u001b[39m     \n \u001b[90m 27 |\u001b[39m     \u001b[90m// Aguardar a tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 28 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Verificar se há gestantes na lista\u001b[39m\n \u001b[90m 31 |\u001b[39m     \u001b[36mconst\u001b[39m rows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 28}, "message": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 26 |\u001b[39m     \n \u001b[90m 27 |\u001b[39m     \u001b[90m// Aguardar a tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 28 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Verificar se há gestantes na lista\u001b[39m\n \u001b[90m 31 |\u001b[39m     \u001b[36mconst\u001b[39m rows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"pregnant-table\"] tbody tr'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:28:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:47.612Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 28}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-9bf3bb4218fa627ef087", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 35, "parallelIndex": 2, "status": "failed", "duration": 11626, "error": {"message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n", "stack": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:39:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 39}, "snippet": "\u001b[0m \u001b[90m 37 |\u001b[39m     \n \u001b[90m 38 |\u001b[39m     \u001b[90m// Clicar no botão \"Nova Gestante\"\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m     \n \u001b[90m 41 |\u001b[39m     \u001b[90m// Aguardar modal abrir\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 39}, "message": "Error: page.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"new-pregnant-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 37 |\u001b[39m     \n \u001b[90m 38 |\u001b[39m     \u001b[90m// Clicar no botão \"Nova Gestante\"\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"new-pregnant-button\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m     \n \u001b[90m 41 |\u001b[39m     \u001b[90m// Aguardar modal abrir\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-form-modal\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:39:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:48.327Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 39}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-49aa9eddc6ede56e0de6", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 36, "parallelIndex": 1, "status": "failed", "duration": 7643, "error": {"message": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n", "stack": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:63:16", "location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m     \n \u001b[90m 62 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Clicar no primeiro botão de editar\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"pregnant-table\"] button:has-text(\"Editar\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 63}, "message": "Error: page.waitForSelector: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"pregnant-table\"]') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m     \n \u001b[90m 62 |\u001b[39m     \u001b[90m// Aguardar tabela carregar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"pregnant-table\"]'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// Clicar no primeiro botão de editar\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"pregnant-table\"] button:has-text(\"Editar\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts:63:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-28T22:18:52.274Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\test-results\\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\gestão-materna-integrada\\src\\test\\e2e\\crud.e2e.test.ts", "column": 16, "line": 63}}], "status": "unexpected"}], "id": "a1fcc6cf2e2dfa1aa572-1020e53ddbe106589ff8", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-20048969b307474cc6a4", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-a07b5042ffffdf9aa6e9", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-68063a29addeb0a3899e", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-b656c0b1b44b3d24b101", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-200d78db7029839589a6", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-4dac537cc7f7b3ae9037", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-1c58d434b6cff7ae8081", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-68cc508afe17c404f959", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-cabbf0a4ece9208f0c8c", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-ee2f593701cc9a08d2e6", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-3597363ce1a79890aa17", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-4d27b687db40ae34309f", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-c28cef1ce0d8ef495c5f", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-38e9a936c490c00b71e2", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-75d8c2deef7abd61b500", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-85b4066117d4d04d4849", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-7412c79ee37cf2e0292a", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-f44256725f301add0f66", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-0b12791571de864d8bb4", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-3ec0ca87150aa0a54eed", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-94097750d7b32d41204a", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-c2b963f5e853209d2df8", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-8cb29e0e1ba2313326a5", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-cd841d76e28b1c32cd89", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-bf6f179e7175e01fd4fb", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-ee6370e48e76ea7cef79", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-68e37e728f568c36b0f4", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-d54e823bfd9b0b7f73f6", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-aa97c798c79dce4026d1", "file": "crud.e2e.test.ts", "line": 225, "column": 3}, {"title": "deve navegar para página de gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-27fcb6579159e9213843", "file": "crud.e2e.test.ts", "line": 15, "column": 3}, {"title": "deve listar gestantes existentes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-82c0ec959a1afe716518", "file": "crud.e2e.test.ts", "line": 24, "column": 3}, {"title": "deve criar nova gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-318e6d8362d8d5594c98", "file": "crud.e2e.test.ts", "line": 35, "column": 3}, {"title": "deve editar gestante existente", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-e51f8b61d3d2f259593e", "file": "crud.e2e.test.ts", "line": 59, "column": 3}, {"title": "deve excluir gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-f212326589616a492eec", "file": "crud.e2e.test.ts", "line": 88, "column": 3}, {"title": "deve buscar gestantes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-895e34f8b5229a84a2c9", "file": "crud.e2e.test.ts", "line": 116, "column": 3}, {"title": "deve validar campos obrigatórios", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-aa8732d170c7abd45883", "file": "crud.e2e.test.ts", "line": 142, "column": 3}, {"title": "deve cancelar criação de gestante", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-cfdf294cd2b1915de44c", "file": "crud.e2e.test.ts", "line": 160, "column": 3}, {"title": "deve exibir mensagens de erro para dados inválidos", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-98c6e4122bd98ef1d676", "file": "crud.e2e.test.ts", "line": 180, "column": 3}, {"title": "deve funcionar em dispositivos móveis", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-89412625a613666bfb8d", "file": "crud.e2e.test.ts", "line": 203, "column": 3}, {"title": "deve manter estado durante nave<PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a1fcc6cf2e2dfa1aa572-b2050feb03a22eff1c70", "file": "crud.e2e.test.ts", "line": 225, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-05-28T22:16:54.697Z", "duration": 125867.58600000001, "expected": 0, "skipped": 42, "unexpected": 35, "flaky": 0}}