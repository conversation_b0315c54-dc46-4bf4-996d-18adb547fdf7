import { Client, LocalAuth, Message as WhatsAppMessage } from 'whatsapp-web.js';
import { Server as SocketServer } from 'socket.io';
import qrcode from 'qrcode';
import { Message } from '../models/Message';
import { Contact, IContact } from '../models/Contact';
import { GeminiAIService } from './gemini';

export class WhatsAppClient {
  private client: Client;
  private io: SocketServer;
  private geminiService: GeminiAIService;
  private isConnected: boolean = false;

  constructor(io: SocketServer, geminiService: GeminiAIService) {
    this.io = io;
    this.geminiService = geminiService;
    this.client = new Client({
      authStrategy: new LocalAuth(),
      puppeteer: {
        args: ['--no-sandbox']
      }
    });

    this.initialize();
  }

  private async initialize() {
    this.client.on('qr', async (qr) => {
      // Gerar QR Code como string para o console (compacto)
      const qrString = await qrcode.toString(qr, { type: 'terminal', small: true });
      console.log('\n=== QR CODE PARA CONEXÃO DO WHATSAPP ===\n');
      console.log(qrString);
      console.log('\n=======================================\n');
      
      // Enviar QR Code como imagem para o frontend
      const qrCode = await qrcode.toDataURL(qr);
      this.io.emit('qr', qrCode);
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      console.log('\n✅ Cliente WhatsApp conectado com sucesso!\n');
      this.io.emit('status', 'connected');
      this.startFollowUpScheduler();
    });

    this.client.on('message', async (message: WhatsAppMessage) => {
      try {
        const contact = await this.getOrCreateContact(message.from);
        await this.saveMessage(message, contact);

        // Processar mensagem com IA
        const aiResponse = await this.geminiService.generateResponse(contact, message.body);
        
        // Enviar resposta
        await this.sendMessage(message.from, aiResponse.response);

        // Atualizar contato com informações da análise
        await this.updateContactWithAnalysis(contact, aiResponse);

        this.io.emit('message', {
          from: message.from,
          body: message.body,
          timestamp: message.timestamp,
          analysis: aiResponse
        });
      } catch (error) {
        console.error('Erro ao processar mensagem:', error);
        await this.sendMessage(message.from, 'Desculpe, tive um problema ao processar sua mensagem. Por favor, tente novamente em alguns instantes.');
      }
    });

    this.client.on('disconnected', () => {
      this.isConnected = false;
      console.log('\n❌ Cliente WhatsApp desconectado\n');
      this.io.emit('status', 'disconnected');
    });

    await this.client.initialize();
  }

  private async getOrCreateContact(phone: string): Promise<IContact> {
    let contact = await Contact.findOne({ phone });
    if (!contact) {
      const info = await this.client.getContactById(phone);
      contact = await Contact.create({
        phone,
        name: info.name || phone,
        pushname: info.pushname,
        lastInteraction: new Date()
      });
    }
    return contact;
  }

  private async saveMessage(message: WhatsAppMessage, contact: IContact) {
    await Message.create({
      contact: contact._id,
      content: message.body,
      timestamp: message.timestamp,
      type: message.type,
      fromMe: message.fromMe
    });
  }

  private async updateContactWithAnalysis(contact: IContact, analysis: any) {
    await Contact.findByIdAndUpdate(contact._id, {
      lastInteraction: new Date(),
      pregnancyStage: analysis.suggestions?.includes('gestação') ? 'em andamento' : contact.pregnancyStage,
      notes: analysis.needs?.length ? `${contact.notes || ''}\nNecessidades: ${analysis.needs.join(', ')}` : contact.notes
    });
  }

  public async sendMessage(to: string, message: string) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      const response = await this.client.sendMessage(to, message);
      const contact = await this.getOrCreateContact(to);
      await this.saveMessage(response, contact);
      return response;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  public async sendBulkMessages(contacts: string[], message: string) {
    const results = await Promise.allSettled(
      contacts.map(contact => this.sendMessage(contact, message))
    );
    return results;
  }

  public getStatus() {
    return {
      isConnected: this.isConnected,
      info: this.client.info
    };
  }

  private async startFollowUpScheduler() {
    // Verificar contatos a cada 24 horas
    setInterval(async () => {
      try {
        const contacts = await Contact.find({
          lastInteraction: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        });

        for (const contact of contacts) {
          const followUpMessage = await this.geminiService.generateFollowUpMessage(contact);
          await this.sendMessage(contact.phone, followUpMessage);
        }
      } catch (error) {
        console.error('Erro ao enviar mensagens de acompanhamento:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 horas
  }
} 
