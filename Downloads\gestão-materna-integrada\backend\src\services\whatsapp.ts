import * as wppconnect from '@wppconnect-team/wppconnect';
import { Server as SocketServer } from 'socket.io';
import { Message } from '../models/Message';
import { Contact, IContact } from '../models/Contact';
import { GeminiAIService } from './gemini';

// Função para normalizar telefone do WhatsApp para formato brasileiro
function normalizePhoneToBrazilian(whatsappPhone: string): string {
  try {
    // Remover @c.us se existir
    let phone = whatsappPhone.replace('@c.us', '');

    // Se começar com 55 (código do Brasil), remover
    if (phone.startsWith('55')) {
      phone = phone.substring(2);
    }

    // Verificar se tem 11 dígitos (DDD + 9 + 8 dígitos)
    if (phone.length === 11) {
      const ddd = phone.substring(0, 2);
      const nono = phone.substring(2, 3);
      const primeiros4 = phone.substring(3, 7);
      const ultimos4 = phone.substring(7, 11);

      // Formato brasileiro: (DDD) 9XXXX-XXXX
      return `(${ddd}) ${nono}${primeiros4}-${ultimos4}`;
    }

    // Verificar se tem 10 dígitos mas deveria ter 11 (adicionar 9)
    if (phone.length === 10 && phone.substring(0, 2) !== '11') {
      const ddd = phone.substring(0, 2);
      const primeiros4 = phone.substring(2, 6);
      const ultimos4 = phone.substring(6, 10);

      // Adicionar 9 para celular: (DDD) 9XXXX-XXXX
      return `(${ddd}) 9${primeiros4}-${ultimos4}`;
    }

    // Se tem 10 dígitos (DDD + 8 dígitos - telefone fixo)
    if (phone.length === 10) {
      const ddd = phone.substring(0, 2);
      const primeiros4 = phone.substring(2, 6);
      const ultimos4 = phone.substring(6, 10);

      // Formato brasileiro: (DDD) XXXX-XXXX
      return `(${ddd}) ${primeiros4}-${ultimos4}`;
    }

    // Se não conseguir normalizar, retornar original
    console.warn('⚠️ Não foi possível normalizar telefone:', whatsappPhone);
    return whatsappPhone;

  } catch (error) {
    console.error('❌ Erro ao normalizar telefone:', error);
    return whatsappPhone;
  }
}

// Função para converter telefone brasileiro para formato WhatsApp
function phoneToWhatsAppFormat(brazilianPhone: string): string {
  try {
    // Remover caracteres especiais
    let phone = brazilianPhone.replace(/[^\d]/g, '');

    // Se não tem código do país, adicionar 55
    if (!phone.startsWith('55')) {
      phone = '55' + phone;
    }

    return phone + '@c.us';
  } catch (error) {
    console.error('❌ Erro ao converter para formato WhatsApp:', error);
    return brazilianPhone;
  }
}

export class WhatsAppClient {
  private client: any;
  private io: SocketServer;
  private geminiService: GeminiAIService;
  private isConnected: boolean = false;

  constructor(io: SocketServer, geminiService: GeminiAIService) {
    this.io = io;
    this.geminiService = geminiService;
    this.initialize();
  }

  private async initialize() {
    try {
      console.log('🔌 Iniciando WPPConnect...');

      this.client = await wppconnect.create({
        session: 'gestao-materna',
        catchQR: (base64Qr, asciiQR) => {
          console.log('\n=== QR CODE PARA CONEXÃO DO WHATSAPP ===\n');
          console.log(asciiQR);
          console.log('\n=======================================\n');

          // Enviar QR Code para o frontend
          this.io.emit('qr', base64Qr);
        },
        statusFind: (statusSession, session) => {
          console.log('📱 Status da sessão:', statusSession, session);

          if (statusSession === 'qrReadSuccess') {
            console.log('✅ QR Code escaneado com sucesso!');
            this.io.emit('status', 'qr_scanned');
          } else if (statusSession === 'isLogged') {
            this.isConnected = true;
            console.log('\n✅ Cliente WhatsApp conectado com sucesso!\n');
            this.io.emit('status', 'connected');
            this.startFollowUpScheduler();
          } else if (statusSession === 'notLogged') {
            this.isConnected = false;
            console.log('❌ WhatsApp não está logado');
            this.io.emit('status', 'disconnected');
          } else if (statusSession === 'autocloseCalled') {
            this.isConnected = false;
            console.log('⏰ Sessão fechada automaticamente');
            this.io.emit('status', 'timeout');
          } else if (statusSession === 'desconnectedMobile') {
            this.isConnected = false;
            console.log('📱 Celular desconectado');
            this.io.emit('status', 'mobile_disconnected');
          } else if (statusSession === 'deviceNotConnected') {
            this.isConnected = false;
            console.log('📱 Dispositivo não conectado');
            this.io.emit('status', 'device_not_connected');
          } else if (statusSession === 'browserClose') {
            this.isConnected = false;
            console.log('🌐 Browser fechado');
            this.io.emit('status', 'browser_closed');
          } else if (statusSession === 'qrReadFail') {
            this.isConnected = false;
            console.log('❌ Falha ao ler QR Code');
            this.io.emit('status', 'qr_failed');
          } else if (statusSession === 'chatsAvailable') {
            this.isConnected = true;
            console.log('💬 Chats disponíveis - WhatsApp totalmente conectado!');
            this.io.emit('status', 'ready');
          }
        },
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: true,
        browserArgs: ['--no-sandbox', '--disable-setuid-sandbox'],
        autoClose: 60000,
        puppeteerOptions: {
          userDataDir: './tokens/gestao-materna'
        }
      });

      // Configurar listener de mensagens
      this.client.onMessage(async (message: any) => {
        try {
          // Ignorar mensagens próprias
          if (message.fromMe) {
            console.log('📤 Mensagem própria ignorada');
            return;
          }

          // Ignorar mensagens criptografadas (sincronização inicial)
          if (message.type === 'ciphertext' || !message.body || message.body === 'undefined') {
            console.log('🔐 Mensagem criptografada/sincronização ignorada');
            return;
          }

          console.log('\n📱 ===== NOVA MENSAGEM RECEBIDA =====');
          console.log('📞 De:', message.from);
          console.log('💬 Conteúdo:', message.body);
          console.log('⏰ Timestamp:', new Date(message.timestamp * 1000).toLocaleString('pt-BR'));
          console.log('📋 Tipo:', message.type);
          console.log('👤 De mim:', message.fromMe);
          console.log('📱 Chat ID:', message.chatId);
          console.log('=====================================\n');

          const contact = await this.getOrCreateContact(message.from);
          console.log('👥 Contato processado:', {
            id: contact._id,
            name: contact.name,
            phone: contact.phone,
            pregnancyStage: contact.pregnancyStage
          });

          await this.saveMessage(message, contact);
          console.log('💾 Mensagem salva no MongoDB');

          // Processar mensagem com IA
          console.log('🤖 Iniciando processamento com IA Gemini...');
          const aiResponse = await this.geminiService.generateResponse(contact, message.body);
          console.log('🧠 Resposta da IA gerada:', {
            response: aiResponse.response.substring(0, 100) + '...',
            sentiment: aiResponse.sentiment,
            needs: aiResponse.needs,
            suggestions: aiResponse.suggestions
          });

          // Enviar resposta
          console.log('📤 Enviando resposta automática...');
          await this.sendMessage(message.from, aiResponse.response);
          console.log('✅ Resposta enviada com sucesso');

          // Atualizar contato com informações da análise
          await this.updateContactWithAnalysis(contact, aiResponse);
          console.log('📊 Contato atualizado com análise da IA');

          // Emitir evento para frontend
          const eventData = {
            from: message.from,
            body: message.body,
            timestamp: message.timestamp,
            analysis: aiResponse,
            contact: {
              id: contact._id,
              name: contact.name,
              phone: contact.phone
            }
          };

          this.io.emit('message', eventData);
          this.io.emit('newMessage', eventData);
          console.log('📡 Evento emitido para frontend');
          console.log('\n🎉 ===== PROCESSAMENTO CONCLUÍDO =====\n');

        } catch (error) {
          console.error('\n❌ ===== ERRO NO PROCESSAMENTO =====');
          console.error('📱 Mensagem:', message.body);
          console.error('📞 De:', message.from);
          console.error('🚨 Erro:', error);
          console.error('=====================================\n');

          await this.sendMessage(message.from, 'Desculpe, tive um problema ao processar sua mensagem. Por favor, tente novamente em alguns instantes.');
        }
      });

    } catch (error) {
      console.error('❌ Erro ao inicializar WPPConnect:', error);
      this.io.emit('status', 'error');
    }
  }



  private async getOrCreateContact(whatsappPhone: string): Promise<IContact> {
    // Normalizar telefone para formato brasileiro
    const brazilianPhone = normalizePhoneToBrazilian(whatsappPhone);
    console.log('📞 Telefone normalizado:', whatsappPhone, '→', brazilianPhone);

    // Buscar contato pelo telefone brasileiro
    let contact = await Contact.findOne({ phone: brazilianPhone });

    if (!contact) {
      try {
        // Tentar obter informações do contato
        const info = await this.client.getContact(whatsappPhone);
        contact = await Contact.create({
          phone: brazilianPhone, // Salvar no formato brasileiro
          name: info.name || info.pushname || brazilianPhone,
          pushname: info.pushname,
          lastInteraction: new Date(),
          isActive: true,
          pregnancyStage: 'first_trimester',
          priority: 'medium',
          isHighRisk: false
        });
        console.log('👤 Novo contato criado:', contact.name, 'Tel:', brazilianPhone);
      } catch (error) {
        // Se não conseguir obter info, criar com dados básicos
        contact = await Contact.create({
          phone: brazilianPhone, // Salvar no formato brasileiro
          name: brazilianPhone,
          lastInteraction: new Date(),
          isActive: true,
          pregnancyStage: 'first_trimester',
          priority: 'medium',
          isHighRisk: false
        });
        console.log('👤 Novo contato criado (básico):', contact.name, 'Tel:', brazilianPhone);
      }
    } else {
      // Atualizar última interação
      contact.lastInteraction = new Date();
      await contact.save();
      console.log('👤 Contato existente encontrado:', contact.name, 'Tel:', brazilianPhone);
    }
    return contact;
  }

  private async saveMessage(message: any, contact: IContact) {
    await Message.create({
      contact: contact._id,
      content: message.body,
      timestamp: message.timestamp,
      type: message.type,
      fromMe: message.fromMe
    });
  }

  private async updateContactWithAnalysis(contact: IContact, analysis: any) {
    await Contact.findByIdAndUpdate(contact._id, {
      lastInteraction: new Date(),
      pregnancyStage: analysis.suggestions?.includes('gestação') ? 'em andamento' : contact.pregnancyStage,
      notes: analysis.needs?.length ? `${contact.notes || ''}\nNecessidades: ${analysis.needs.join(', ')}` : contact.notes
    });
  }

  public async sendMessage(to: string, message: string) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      // Converter telefone brasileiro para formato WhatsApp se necessário
      let whatsappPhone = to;
      if (!to.includes('@c.us')) {
        whatsappPhone = phoneToWhatsAppFormat(to);
        console.log('📞 Telefone convertido para WhatsApp:', to, '→', whatsappPhone);
      }

      console.log('📤 Enviando mensagem para:', whatsappPhone);
      const response = await this.client.sendText(whatsappPhone, message);

      // Salvar mensagem enviada (usar telefone WhatsApp para buscar contato)
      const contact = await this.getOrCreateContact(whatsappPhone);
      await Message.create({
        contact: contact._id,
        content: message,
        timestamp: Date.now(),
        type: 'chat',
        fromMe: true
      });

      console.log('✅ Mensagem enviada e salva');
      return response;
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  public async sendBulkMessages(contacts: string[], message: string) {
    const results = await Promise.allSettled(
      contacts.map(contact => this.sendMessage(contact, message))
    );
    return results;
  }

  public getStatus() {
    return {
      isConnected: this.isConnected,
      info: this.client.info
    };
  }

  private async startFollowUpScheduler() {
    // Verificar contatos a cada 24 horas, mas enviar follow-up apenas após 15 dias sem contato
    setInterval(async () => {
      try {
        console.log('🔍 Verificando contatos para follow-up (15 dias sem contato)...');

        const fifteenDaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000);
        const contacts = await Contact.find({
          isActive: true,
          lastInteraction: { $lt: fifteenDaysAgo }
        });

        console.log(`📋 Encontrados ${contacts.length} contatos sem interação há 15+ dias`);

        for (const contact of contacts) {
          try {
            console.log(`📤 Enviando follow-up para ${contact.name} (última interação: ${contact.lastInteraction?.toLocaleDateString('pt-BR')})`);

            const followUpMessage = await this.geminiService.generateFollowUpMessage(contact);
            await this.sendMessage(contact.phone, followUpMessage);

            // Atualizar última interação para evitar spam
            contact.lastInteraction = new Date();
            await contact.save();

            console.log(`✅ Follow-up enviado para ${contact.name}`);

            // Delay entre mensagens para não sobrecarregar
            await new Promise(resolve => setTimeout(resolve, 3000));
          } catch (error) {
            console.error(`❌ Erro ao enviar follow-up para ${contact.name}:`, error);
          }
        }

        console.log('✅ Verificação de follow-up concluída');
      } catch (error) {
        console.error('❌ Erro ao verificar follow-ups:', error);
      }
    }, 24 * 60 * 60 * 1000); // Verifica a cada 24 horas
  }
} 
