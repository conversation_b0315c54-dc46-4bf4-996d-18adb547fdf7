"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const UserSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: [true, 'Nome é obrigatório'],
        trim: true,
        minlength: [2, 'Nome deve ter pelo menos 2 caracteres'],
        maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
    },
    email: {
        type: String,
        required: [true, 'Email é obrigatório'],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email inválido']
    },
    password: {
        type: String,
        required: [true, 'Senha é obrigatória'],
        minlength: [8, 'Senha deve ter pelo menos 8 caracteres'],
        select: false // Por padrão, não incluir a senha nas consultas
    },
    role: {
        type: String,
        enum: ['admin', 'nurse', 'doctor', 'coordinator'],
        default: 'nurse',
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastLogin: {
        type: Date
    },
    permissions: [String]
}, {
    timestamps: true
});
// Índices para performance (email já tem índice único automático)
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
// Middleware para hash da senha antes de salvar
UserSchema.pre('save', async function (next) {
    if (!this.isModified('password'))
        return next();
    try {
        const salt = await bcryptjs_1.default.genSalt(12);
        this.password = await bcryptjs_1.default.hash(this.password, salt);
        next();
    }
    catch (error) {
        next(error);
    }
});
// Middleware para definir permissões baseadas no role
UserSchema.pre('save', function (next) {
    if (!this.isModified('role'))
        return next();
    const rolePermissions = {
        admin: [
            'read:contacts', 'write:contacts', 'delete:contacts',
            'read:messages', 'write:messages',
            'read:analytics', 'manage:users', 'manage:system',
            'send:bulk_messages', 'access:ai_features'
        ],
        doctor: [
            'read:contacts', 'write:contacts',
            'read:messages', 'write:messages',
            'read:analytics', 'access:ai_features'
        ],
        coordinator: [
            'read:contacts', 'write:contacts',
            'read:messages', 'write:messages',
            'read:analytics', 'send:bulk_messages', 'access:ai_features'
        ],
        nurse: [
            'read:contacts', 'write:contacts',
            'read:messages', 'write:messages'
        ]
    };
    this.permissions = rolePermissions[this.role] || [];
    next();
});
// Método para comparar senhas
UserSchema.methods.comparePassword = async function (candidatePassword) {
    return bcryptjs_1.default.compare(candidatePassword, this.password);
};
// Método para gerar token JWT
UserSchema.methods.generateAuthToken = function () {
    const payload = {
        id: this._id,
        email: this.email,
        role: this.role,
        permissions: this.permissions
    };
    return jsonwebtoken_1.default.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', { expiresIn: '24h' });
};
// Método estático para login
UserSchema.statics.findByCredentials = async function (email, password) {
    const user = await this.findOne({ email, isActive: true }).select('+password');
    if (!user) {
        throw new Error('Credenciais inválidas');
    }
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
        throw new Error('Credenciais inválidas');
    }
    // Atualizar último login
    user.lastLogin = new Date();
    await user.save();
    return user;
};
// Método para obter usuário sem dados sensíveis
UserSchema.methods.toJSON = function () {
    const user = this.toObject();
    delete user.password;
    return user;
};
exports.User = mongoose_1.default.model('User', UserSchema);
