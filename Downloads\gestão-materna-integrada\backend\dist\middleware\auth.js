"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateResourceOwnership = exports.auditLog = exports.rateLimitByUser = exports.optionalAuth = exports.requireHealthProfessional = exports.requireAdminOrCoordinator = exports.requireAdmin = exports.authorizeRole = exports.authorize = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = require("../models/User");
// Middleware de autenticação
const authenticate = async (req, res, next) => {
    var _a;
    try {
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({
                error: 'Token de acesso requerido',
                code: 'NO_TOKEN'
            });
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback-secret-key');
        const user = await User_1.User.findById(decoded.id);
        if (!user || !user.isActive) {
            return res.status(401).json({
                error: 'Token inválido ou usuário inativo',
                code: 'INVALID_TOKEN'
            });
        }
        req.user = user;
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            return res.status(401).json({
                error: 'Token expirado',
                code: 'TOKEN_EXPIRED'
            });
        }
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            return res.status(401).json({
                error: 'Token inválido',
                code: 'INVALID_TOKEN'
            });
        }
        return res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
};
exports.authenticate = authenticate;
// Middleware de autorização por permissão
const authorize = (requiredPermission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Usuário não autenticado',
                code: 'NOT_AUTHENTICATED'
            });
        }
        if (!req.user.permissions.includes(requiredPermission)) {
            return res.status(403).json({
                error: 'Permissão insuficiente',
                code: 'INSUFFICIENT_PERMISSION',
                required: requiredPermission,
                userPermissions: req.user.permissions
            });
        }
        next();
    };
};
exports.authorize = authorize;
// Middleware de autorização por role
const authorizeRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Usuário não autenticado',
                code: 'NOT_AUTHENTICATED'
            });
        }
        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).json({
                error: 'Role insuficiente',
                code: 'INSUFFICIENT_ROLE',
                required: allowedRoles,
                userRole: req.user.role
            });
        }
        next();
    };
};
exports.authorizeRole = authorizeRole;
// Middleware para verificar se é admin
exports.requireAdmin = (0, exports.authorizeRole)(['admin']);
// Middleware para verificar se é admin ou coordinator
exports.requireAdminOrCoordinator = (0, exports.authorizeRole)(['admin', 'coordinator']);
// Middleware para verificar se é profissional de saúde
exports.requireHealthProfessional = (0, exports.authorizeRole)(['admin', 'doctor', 'coordinator']);
// Middleware opcional de autenticação (não falha se não houver token)
const optionalAuth = async (req, res, next) => {
    var _a;
    try {
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', '');
        if (token) {
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback-secret-key');
            const user = await User_1.User.findById(decoded.id);
            if (user && user.isActive) {
                req.user = user;
            }
        }
        next();
    }
    catch (error) {
        // Em caso de erro, continua sem usuário autenticado
        next();
    }
};
exports.optionalAuth = optionalAuth;
// Middleware para rate limiting por usuário
const rateLimitByUser = (maxRequests, windowMs) => {
    const userRequests = new Map();
    return (req, res, next) => {
        if (!req.user) {
            return next();
        }
        const userId = req.user._id.toString();
        const now = Date.now();
        const userLimit = userRequests.get(userId);
        if (!userLimit || now > userLimit.resetTime) {
            userRequests.set(userId, {
                count: 1,
                resetTime: now + windowMs
            });
            return next();
        }
        if (userLimit.count >= maxRequests) {
            return res.status(429).json({
                error: 'Muitas requisições',
                code: 'RATE_LIMIT_EXCEEDED',
                retryAfter: Math.ceil((userLimit.resetTime - now) / 1000)
            });
        }
        userLimit.count++;
        next();
    };
};
exports.rateLimitByUser = rateLimitByUser;
// Middleware para log de auditoria
const auditLog = (action) => {
    return (req, res, next) => {
        const originalSend = res.send;
        res.send = function (data) {
            // Log da ação após resposta
            if (req.user) {
                console.log(`[AUDIT] ${new Date().toISOString()} - User: ${req.user.email} - Action: ${action} - IP: ${req.ip} - Status: ${res.statusCode}`);
            }
            return originalSend.call(this, data);
        };
        next();
    };
};
exports.auditLog = auditLog;
// Middleware para validar propriedade do recurso
const validateResourceOwnership = (resourceModel, resourceIdParam = 'id') => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({
                    error: 'Usuário não autenticado',
                    code: 'NOT_AUTHENTICATED'
                });
            }
            // Admin pode acessar qualquer recurso
            if (req.user.role === 'admin') {
                return next();
            }
            const resourceId = req.params[resourceIdParam];
            const resource = await resourceModel.findById(resourceId);
            if (!resource) {
                return res.status(404).json({
                    error: 'Recurso não encontrado',
                    code: 'RESOURCE_NOT_FOUND'
                });
            }
            // Verificar se o usuário é dono do recurso (se aplicável)
            if (resource.createdBy && resource.createdBy.toString() !== req.user._id.toString()) {
                return res.status(403).json({
                    error: 'Acesso negado ao recurso',
                    code: 'RESOURCE_ACCESS_DENIED'
                });
            }
            next();
        }
        catch (error) {
            return res.status(500).json({
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            });
        }
    };
};
exports.validateResourceOwnership = validateResourceOwnership;
