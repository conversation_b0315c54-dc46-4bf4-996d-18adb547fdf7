import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { Contact, IContact } from '../models/Contact';
import { Message } from '../models/Message';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();

// Validações simplificadas (apenas 3 campos)
const contactValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('phone')
    .trim()
    .matches(/^\+?[\d\s\-\(\)]+$/)
    .withMessage('Formato de telefone inválido'),
  body('babyGender')
    .optional()
    .isIn(['male', 'female', 'unknown'])
    .withMessage('Gênero do bebê inválido')
];

// GET /api/contacts - Listar contatos com filtros e paginação
router.get('/',
  authenticate,
  authorize('read:contacts'),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Página deve ser um número positivo'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100'),
    query('search').optional().isLength({ max: 100 }).withMessage('Busca deve ter no máximo 100 caracteres'),
    query('babyGender').optional().isIn(['male', 'female', 'unknown']),
    query('sortBy').optional().isIn(['name', 'lastInteraction', 'createdAt']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;
      const babyGender = req.query.babyGender as string;
      const sortBy = req.query.sortBy as string || 'lastInteraction';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Construir filtros
      const filters: any = { isActive: true };

      // Filtro por usuário (não-admin vê apenas seus contatos)
      if (req.user!.role !== 'admin') {
        filters.$or = [
          { createdBy: req.user!._id },
          { assignedTo: req.user!._id }
        ];
      }

      if (search) {
        filters.$or = [
          { name: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } }
        ];
      }

      if (babyGender) filters.babyGender = babyGender;

      // Executar consulta
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      const [contacts, total] = await Promise.all([
        Contact.find(filters)
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .populate('createdBy', 'name email')
          .populate('assignedTo', 'name email')
          .lean(),
        Contact.countDocuments(filters)
      ]);

      // Dados simplificados (sem cálculos complexos)
      const enrichedContacts = contacts;

      res.json({
        contacts: enrichedContacts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          search,
          babyGender
        }
      });
    } catch (error) {
      console.error('Erro ao listar contatos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/:id - Obter contato específico
router.get('/:id',
  authenticate,
  authorize('read:contacts'),
  async (req, res) => {
    try {
      const contact = await Contact.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('assignedTo', 'name email');

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Verificar permissão (não-admin só vê seus contatos)
      if (req.user!.role !== 'admin' &&
          contact.createdBy?.toString() !== (req.user!._id as any).toString() &&
          contact.assignedTo?.toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      // Buscar estatísticas de mensagens
      const messageStats = await Message.aggregate([
        { $match: { contact: contact._id } },
        {
          $group: {
            _id: null,
            totalMessages: { $sum: 1 },
            lastMessage: { $max: '$timestamp' },
            fromMeCount: { $sum: { $cond: ['$fromMe', 1, 0] } },
            fromContactCount: { $sum: { $cond: ['$fromMe', 0, 1] } }
          }
        }
      ]);

      const stats = messageStats[0] || {
        totalMessages: 0,
        lastMessage: null,
        fromMeCount: 0,
        fromContactCount: 0
      };

      res.json({
        contact: {
          ...contact.toObject(),
          gestationalAge: contact.getGestationalAge(),
          pregnancyStatus: contact.getPregnancyStatus(),
          isOverdue: contact.isOverdue()
        },
        messageStats: stats
      });
    } catch (error) {
      console.error('Erro ao obter contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/contacts - Criar novo contato
router.post('/',
  authenticate,
  authorize('write:contacts'),
  contactValidation,
  auditLog('CREATE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se telefone já existe
      const existingContact = await Contact.findOne({ 
        phone: req.body.phone,
        isActive: true 
      });

      if (existingContact) {
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Criar contato
      const contactData = {
        ...req.body,
        createdBy: req.user!._id,
        assignedTo: req.body.assignedTo || req.user!._id
      };

      const contact = new Contact(contactData);
      await contact.save();

      await contact.populate('createdBy', 'name email');
      await contact.populate('assignedTo', 'name email');

      res.status(201).json({
        message: 'Contato criado com sucesso',
        contact: contact.toObject()
      });
    } catch (error) {
      console.error('Erro ao criar contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/contacts/:id - Atualizar contato
router.put('/:id',
  authenticate,
  authorize('write:contacts'),
  contactValidation,
  auditLog('UPDATE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' &&
          contact.createdBy?.toString() !== (req.user!._id as any).toString() &&
          contact.assignedTo?.toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      // Verificar se telefone já existe (se alterado)
      if (req.body.phone && req.body.phone !== contact.phone) {
        const existingContact = await Contact.findOne({
          phone: req.body.phone,
          isActive: true,
          _id: { $ne: contact._id }
        });

        if (existingContact) {
          return res.status(409).json({
            error: 'Telefone já cadastrado',
            code: 'PHONE_ALREADY_EXISTS'
          });
        }
      }

      // Atualizar contato
      Object.assign(contact, req.body);
      contact.lastInteraction = new Date();
      await contact.save();

      await contact.populate('createdBy', 'name email');
      await contact.populate('assignedTo', 'name email');

      res.json({
        message: 'Contato atualizado com sucesso',
        contact: contact.toObject()
      });
    } catch (error) {
      console.error('Erro ao atualizar contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// DELETE /api/contacts/:id - Excluir contato (soft delete)
router.delete('/:id',
  authenticate,
  authorize('delete:contacts'),
  auditLog('DELETE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' &&
          contact.createdBy?.toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      // Soft delete
      contact.isActive = false;
      await contact.save();

      res.json({
        message: 'Contato excluído com sucesso'
      });
    } catch (error) {
      console.error('Erro ao excluir contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/:id/messages - Obter mensagens do contato
router.get('/:id/messages',
  authenticate,
  authorize('read:messages'),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const contact = await Contact.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' &&
          contact.createdBy?.toString() !== (req.user!._id as any).toString() &&
          contact.assignedTo?.toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        Message.find({ contact: contact._id })
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Message.countDocuments({ contact: contact._id })
      ]);

      res.json({
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Erro ao obter mensagens:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/stats - Estatísticas dos contatos
router.get('/stats/overview',
  authenticate,
  authorize('read:analytics'),
  async (req, res) => {
    try {
      const filters: any = { isActive: true };

      // Filtro por usuário (não-admin vê apenas seus contatos)
      if (req.user!.role !== 'admin') {
        filters.$or = [
          { createdBy: req.user!._id },
          { assignedTo: req.user!._id }
        ];
      }

      const stats = await Contact.aggregate([
        { $match: filters },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            male: { $sum: { $cond: [{ $eq: ['$babyGender', 'male'] }, 1, 0] } },
            female: { $sum: { $cond: [{ $eq: ['$babyGender', 'female'] }, 1, 0] } },
            unknown: { $sum: { $cond: [{ $eq: ['$babyGender', 'unknown'] }, 1, 0] } }
          }
        }
      ]);

      const result = stats[0] || {
        total: 0,
        male: 0,
        female: 0,
        unknown: 0
      };

      res.json(result);
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// Funções auxiliares
function calculateGestationalAge(dueDate: Date): number | null {
  const today = new Date();
  const due = new Date(dueDate);
  const gestationPeriod = 40 * 7; // 40 semanas em dias
  const daysSinceConception = gestationPeriod - Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  return Math.max(0, Math.floor(daysSinceConception / 7));
}

function getPregnancyStatus(dueDate: Date): string {
  const weeks = calculateGestationalAge(dueDate);
  if (!weeks) return 'Não informado';

  if (weeks < 13) return 'Primeiro trimestre';
  if (weeks < 27) return 'Segundo trimestre';
  if (weeks < 40) return 'Terceiro trimestre';
  return 'Pós-termo';
}

export default router;
