2025/06/04-01:44:37.214 54bc Reusing MANIFEST C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/04-01:44:37.214 54bc Recovering log #490
2025/06/04-01:44:37.224 54bc Reusing old log C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000490.log 
2025/06/04-01:44:37.224 54bc Delete type=2 #487
2025/06/04-01:44:37.224 54bc Delete type=2 #488
2025/06/04-01:44:39.404 2840 Level-0 table #496: started
2025/06/04-01:44:39.406 2840 Level-0 table #496: 130972 bytes OK
2025/06/04-01:44:39.407 2840 Delete type=0 #490
2025/06/04-01:44:39.407 2840 Manual compaction at level-0 from '\x00\xa1\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\xa2\x00\x00\x00' @ 0 : 0; will stop at '\x00\xa1\x00\x00\x05' @ 484705 : 1
2025/06/04-01:44:39.408 2840 Compacting 1@0 + 2@1 files
2025/06/04-01:44:39.510 2840 Generated table #497@0: 143002 keys, 2311664 bytes
2025/06/04-01:44:39.535 2840 Generated table #498@0: 37914 keys, 1024796 bytes
2025/06/04-01:44:39.536 2840 Compacted 1@0 + 2@1 files => 3336460 bytes
2025/06/04-01:44:39.536 2840 compacted to: files[ 0 2 1 0 0 0 0 ]
2025/06/04-01:44:39.536 2840 Delete type=2 #496
2025/06/04-01:44:39.536 2840 Manual compaction at level-0 from '\x00\xa1\x00\x00\x05' @ 484705 : 1 .. '\x00\xa2\x00\x00\x00' @ 0 : 0; will stop at (end)
