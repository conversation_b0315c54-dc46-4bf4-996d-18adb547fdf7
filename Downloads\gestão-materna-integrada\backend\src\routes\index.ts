import express from 'express';
import { WhatsAppClient } from '../services/whatsapp';
import { GeminiAIService } from '../services/gemini';
import { Contact } from '../models/Contact';
import { Message } from '../models/Message';
import authRoutes from './auth';
import contactRoutes from './contacts';
import messageRoutes from './messages';
import analyticsRoutes from './analytics';
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';
import scheduleRoutes from './schedules';
import templateRoutes from './templates';

export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // ROTAS SIMPLES SEM AUTENTICAÇÃO (DEVEM VIR PRIMEIRO)

  // Rota simples para listar contatos (sem autenticação - compatibilidade)
  app.get('/api/contacts', async (req, res) => {
    try {
      console.log('🔍 Requisição /api/contacts recebida (sem auth)');
      // Buscar gestantes ativas com apenas campos simplificados
      const contacts = await Contact.find({ isActive: true })
        .select('_id name phone babyGender isActive lastInteraction createdAt updatedAt')
        .sort({ lastInteraction: -1 })
        .limit(100)
        .lean();

      console.log(`👥 Lista de gestantes solicitada - Encontradas: ${contacts.length}`);
      res.json(contacts);
    } catch (error: any) {
      console.error('❌ Erro ao listar contatos:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota simples para criar contato (sem autenticação - compatibilidade)
  app.post('/api/contacts', async (req, res) => {
    try {
      console.log('📝 Requisição POST /api/contacts recebida (sem auth)');
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, babyGender } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      // Verificar se telefone já existe em contatos ativos
      const existingActiveContact = await Contact.findOne({
        phone: phone,
        isActive: true
      });

      if (existingActiveContact) {
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Verificar se existe contato inativo com mesmo telefone para reativar
      const existingInactiveContact = await Contact.findOne({
        phone: phone,
        isActive: false
      });

      if (existingInactiveContact) {
        console.log('📞 Reativando contato existente:', existingInactiveContact._id);

        // Atualizar contato existente em vez de criar novo
        existingInactiveContact.name = name;
        existingInactiveContact.babyGender = babyGender || 'unknown';
        existingInactiveContact.isActive = true;
        existingInactiveContact.lastInteraction = new Date();

        const reactivatedContact = await existingInactiveContact.save();

        console.log('✅ Contato reativado com sucesso:', reactivatedContact._id);

        return res.status(201).json(reactivatedContact);
      }

      // Criar nova gestante (modelo simplificado)
      const newContact = new Contact({
        name,
        phone,
        babyGender: babyGender || 'unknown',
        isActive: true
      });

      const savedContact = await newContact.save();

      console.log('✅ Gestante criada com sucesso:', savedContact._id);

      res.status(201).json(savedContact);
    } catch (error: any) {
      console.error('❌ Erro ao criar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR',
        details: error.message
      });
    }
  });

  // Rota simples para atualizar contato (sem autenticação - compatibilidade)
  app.put('/api/contacts/:id', async (req, res) => {
    try {
      console.log('📝 Requisição PUT /api/contacts recebida (sem auth)');
      console.log('📝 ID:', req.params.id);
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, babyGender } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      // Atualizar gestante (modelo simplificado)
      const updateData = {
        name,
        phone,
        babyGender: babyGender || 'unknown',
        lastInteraction: new Date()
      };

      const updatedContact = await Contact.findByIdAndUpdate(
        req.params.id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedContact) {
        return res.status(404).json({
          error: 'Gestante não encontrada',
          code: 'NOT_FOUND'
        });
      }

      console.log('✅ Gestante atualizada com sucesso:', req.params.id);

      res.json(updatedContact);
    } catch (error: any) {
      console.error('❌ Erro ao atualizar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota simples para deletar contato (sem autenticação - compatibilidade)
  app.delete('/api/contacts/:id', async (req, res) => {
    try {
      console.log('🗑️ Requisição DELETE /api/contacts recebida (sem auth)');
      console.log('🗑️ ID:', req.params.id);

      // Verificar se é hard delete
      const hardDelete = req.query.hard === 'true';

      if (hardDelete) {
        // Hard delete - remover completamente
        const deletedContact = await Contact.findByIdAndDelete(req.params.id);

        if (!deletedContact) {
          return res.status(404).json({
            error: 'Gestante não encontrada',
            code: 'NOT_FOUND'
          });
        }

        console.log('🗑️ Gestante removida permanentemente:', req.params.id);
        res.json({ message: 'Gestante removida permanentemente' });
      } else {
        // Soft delete - marcar como inativa
        const deletedContact = await Contact.findByIdAndUpdate(
          req.params.id,
          { isActive: false, lastInteraction: new Date() },
          { new: true }
        );

        if (!deletedContact) {
          return res.status(404).json({
            error: 'Gestante não encontrada',
            code: 'NOT_FOUND'
          });
        }

        console.log('✅ Gestante deletada com sucesso (soft delete):', req.params.id);
        res.json({ message: 'Gestante deletada com sucesso' });
      }
    } catch (error: any) {
      console.error('❌ Erro ao deletar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota para limpar contatos inativos (hard delete em lote)
  app.delete('/api/contacts/cleanup/inactive', async (req, res) => {
    try {
      console.log('🧹 Limpeza de contatos inativos solicitada');

      const result = await Contact.deleteMany({ isActive: false });

      console.log(`🧹 ${result.deletedCount} contatos inativos removidos permanentemente`);

      res.json({
        message: `${result.deletedCount} contatos inativos removidos permanentemente`,
        deletedCount: result.deletedCount
      });
    } catch (error: any) {
      console.error('❌ Erro ao limpar contatos inativos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota GET temporária para limpeza (para facilitar teste)
  app.get('/api/contacts/cleanup/inactive', async (req, res) => {
    try {
      console.log('🧹 Limpeza de contatos inativos solicitada (GET)');

      const result = await Contact.deleteMany({ isActive: false });

      console.log(`🧹 ${result.deletedCount} contatos inativos removidos permanentemente`);

      res.json({
        message: `${result.deletedCount} contatos inativos removidos permanentemente`,
        deletedCount: result.deletedCount
      });
    } catch (error: any) {
      console.error('❌ Erro ao limpar contatos inativos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota simples para analytics do dashboard (sem autenticação - compatibilidade)
  app.get('/api/analytics/dashboard', async (req, res) => {
    try {
      console.log('📊 Analytics do dashboard solicitado (sem auth)');

      // Buscar estatísticas dos contatos (simplificado)
      const totalContacts = await Contact.countDocuments({ isActive: true });

      // Estatísticas por gênero do bebê
      const maleGender = await Contact.countDocuments({ isActive: true, babyGender: 'male' });
      const femaleGender = await Contact.countDocuments({ isActive: true, babyGender: 'female' });
      const unknownGender = await Contact.countDocuments({ isActive: true, babyGender: 'unknown' });

      // Estatísticas de mensagens
      const totalMessages = await Message.countDocuments();
      const todayMessages = await Message.countDocuments({
        timestamp: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
      });

      const analytics = {
        totalContacts,
        babyGenders: {
          male: maleGender,
          female: femaleGender,
          unknown: unknownGender
        },
        messages: {
          total: totalMessages,
          today: todayMessages
        },
        lastUpdated: new Date().toISOString()
      };

      console.log(`📊 Analytics calculado - Gestantes encontradas: ${totalContacts}`);

      res.json(analytics);
    } catch (error: any) {
      console.error('❌ Erro ao calcular analytics:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // ROTAS COM AUTENTICAÇÃO (VÊM DEPOIS)

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de mensagens
  app.use('/api/messages', messageRoutes);

  // Rotas de analytics
  app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rotas de agendamentos proativos
  app.use('/api/schedules', scheduleRoutes);

  // Rotas de templates de mensagens
  app.use('/api/templates', templateRoutes);

  // Rota de health check
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Rota para verificar status do WhatsApp
  app.get('/api/whatsapp/status', async (req, res) => {
    try {
      console.log('📱 Status do WhatsApp solicitado');
      const status = whatsappClient.getStatus();
      console.log('📱 Status atual:', status);
      res.json(status);
    } catch (error: any) {
      console.error('❌ Erro ao obter status do WhatsApp:', error);
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota para enviar mensagem
  app.post('/api/whatsapp/send', async (req, res) => {
    try {
      console.log('📤 Enviando mensagem via API:', req.body);
      const { to, message } = req.body;
      const response = await whatsappClient.sendMessage(to, message);
      console.log('✅ Mensagem enviada via API com sucesso');
      res.json({ success: true, response });
    } catch (error: any) {
      console.error('❌ Erro ao enviar mensagem via API:', error);
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota para enviar mensagens em massa
  app.post('/api/whatsapp/bulk', async (req, res) => {
    try {
      const { contacts, message } = req.body;
      const results = await whatsappClient.sendBulkMessages(contacts, message);
      res.json({ success: true, results });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });



  // Rota para obter mensagens de um contato
  app.get('/api/contacts/:id/messages', async (req, res) => {
    try {
      const messages = await Message.find({ contact: req.params.id })
        .sort({ timestamp: -1 })
        .populate('contact');
      res.json(messages);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const response = await geminiService.generateResponse(contact, message);
      res.json(response);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para gerar mensagem de acompanhamento
  app.post('/api/ai/follow-up', async (req, res) => {
    try {
      const { contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const message = await geminiService.generateFollowUpMessage(contact);
      res.json({ message });
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota de migração para limpar dados antigos e manter apenas modelo simplificado
  app.post('/api/migrate/simplify-contacts', async (req, res) => {
    try {
      console.log('🔄 Iniciando migração para modelo simplificado...');

      // Buscar todos os contatos
      const contacts = await Contact.find({});
      console.log(`📋 Encontrados ${contacts.length} contatos para migrar`);

      let migratedCount = 0;

      for (const contact of contacts) {
        try {
          // Dados simplificados que queremos manter
          const simplifiedData = {
            name: contact.name,
            phone: contact.phone,
            babyGender: contact.babyGender || 'unknown',
            isActive: contact.isActive !== false, // Default true se não definido
            lastInteraction: contact.lastInteraction || contact.createdAt || new Date(),
            createdAt: contact.createdAt || new Date(),
            updatedAt: new Date()
          };

          // Remover campos antigos usando $unset
          await Contact.findByIdAndUpdate(
            contact._id,
            {
              $set: simplifiedData,
              $unset: {
                email: "",
                pregnancyStage: "",
                dueDate: "",
                isHighRisk: "",
                complications: "",
                allergies: "",
                medications: "",
                tags: "",
                priority: "",
                observations: "",
                notes: "",
                dateOfBirth: "",
                bloodType: "",
                emergencyContact: "",
                assignedTo: "",
                createdBy: ""
              }
            },
            { new: true }
          );

          migratedCount++;
          console.log(`✅ Contato migrado: ${contact.name} (${contact._id})`);
        } catch (error: any) {
          console.error(`❌ Erro ao migrar contato ${contact._id}:`, error);
        }
      }

      console.log(`🎉 Migração concluída! ${migratedCount}/${contacts.length} contatos migrados`);

      res.json({
        message: 'Migração para modelo simplificado concluída',
        totalContacts: contacts.length,
        migratedContacts: migratedCount,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      console.error('❌ Erro na migração:', error);
      res.status(500).json({
        error: 'Erro na migração',
        code: 'MIGRATION_ERROR',
        details: error.message
      });
    }
  });

  // Rota de debug para visualizar dados do banco
  app.get('/api/debug/database', async (req, res) => {
    try {
      console.log('🔍 Consultando dados do banco...');

      // Buscar todas as coleções
      const collections = await Contact.db.db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      console.log('📊 Coleções encontradas:', collectionNames);

      // Buscar dados de cada coleção
      const databaseData: any = {};

      for (const collectionName of collectionNames) {
        try {
          const collection = Contact.db.db.collection(collectionName);
          const count = await collection.countDocuments();
          const sampleData = await collection.find({}).limit(5).toArray();

          databaseData[collectionName] = {
            count,
            sampleData
          };

          console.log(`📋 ${collectionName}: ${count} documentos`);
        } catch (error: any) {
          console.error(`❌ Erro ao consultar ${collectionName}:`, error);
          databaseData[collectionName] = {
            error: error.message
          };
        }
      }

      res.json({
        message: 'Dados do banco de dados',
        database: process.env.MONGODB_URI ? 'MongoDB Atlas' : 'MongoDB Local',
        collections: collectionNames,
        data: databaseData,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      console.error('❌ Erro ao consultar banco:', error);
      res.status(500).json({
        error: 'Erro ao consultar banco de dados',
        code: 'DATABASE_ERROR',
        details: error.message
      });
    }
  });
}