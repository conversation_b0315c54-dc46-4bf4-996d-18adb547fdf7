import express from 'express';
import { WhatsAppClient } from '../services/whatsapp';
import { GeminiAIService } from '../services/gemini';
import { Contact } from '../models/Contact';
import { Message } from '../models/Message';
import authRoutes from './auth';
import contactRoutes from './contacts';
import messageRoutes from './messages';
import analyticsRoutes from './analytics';
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';
import scheduleRoutes from './schedules';
import templateRoutes from './templates';

export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de contatos (gestantes)
  app.use('/api/contacts', contactRoutes);

  // Rotas de mensagens
  app.use('/api/messages', messageRoutes);

  // Rotas de analytics
  app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rotas de agendamentos proativos
  app.use('/api/schedules', scheduleRoutes);

  // Rotas de templates de mensagens
  app.use('/api/templates', templateRoutes);

  // Rota de health check
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Rota para enviar mensagem
  app.post('/api/whatsapp/send', async (req, res) => {
    try {
      const { to, message } = req.body;
      const response = await whatsappClient.sendMessage(to, message);
      res.json({ success: true, response });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota para enviar mensagens em massa
  app.post('/api/whatsapp/bulk', async (req, res) => {
    try {
      const { contacts, message } = req.body;
      const results = await whatsappClient.sendBulkMessages(contacts, message);
      res.json({ success: true, results });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota simples para listar contatos (sem autenticação - compatibilidade)
  app.get('/api/contacts', async (req, res) => {
    try {
      console.log('🔍 Requisição /api/contacts recebida (sem auth)');
      const contacts = await Contact.find({ isActive: true }).sort({ lastInteraction: -1 });
      console.log(`👥 Lista de gestantes solicitada - Encontradas: ${contacts.length}`);
      res.json(contacts);
    } catch (error: any) {
      console.error('❌ Erro ao listar contatos:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota simples para criar contato (sem autenticação - compatibilidade)
  app.post('/api/contacts', async (req, res) => {
    try {
      console.log('📝 Requisição POST /api/contacts recebida (sem auth)');
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, email, dueDate, trimester, notes } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      // Verificar se telefone já existe
      const existingContact = await Contact.findOne({
        phone: phone,
        isActive: true
      });

      if (existingContact) {
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Mapear trimester para pregnancyStage
      let pregnancyStage = 'first_trimester';
      if (trimester === 2) pregnancyStage = 'second_trimester';
      else if (trimester === 3) pregnancyStage = 'third_trimester';

      // Criar nova gestante
      const newContact = new Contact({
        name,
        phone,
        email: email || '',
        dueDate: dueDate ? new Date(dueDate) : null,
        pregnancyStage,
        observations: notes || '',
        isActive: true,
        priority: 'medium',
        isHighRisk: false
      });

      const savedContact = await newContact.save();

      console.log('✅ Gestante criada com sucesso:', savedContact._id);

      res.status(201).json(savedContact);
    } catch (error: any) {
      console.error('❌ Erro ao criar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota simples para atualizar contato (sem autenticação - compatibilidade)
  app.put('/api/contacts/:id', async (req, res) => {
    try {
      console.log('📝 Requisição PUT /api/contacts recebida (sem auth)');
      console.log('📝 ID:', req.params.id);
      console.log('📝 Dados recebidos:', req.body);

      const { name, phone, email, dueDate, trimester, notes } = req.body;

      // Validações básicas
      if (!name || !phone) {
        return res.status(400).json({
          error: 'Nome e telefone são obrigatórios',
          code: 'MISSING_REQUIRED_FIELDS'
        });
      }

      // Mapear trimester para pregnancyStage
      let pregnancyStage = 'first_trimester';
      if (trimester === 2) pregnancyStage = 'second_trimester';
      else if (trimester === 3) pregnancyStage = 'third_trimester';

      // Atualizar gestante
      const updateData = {
        name,
        phone,
        email: email || '',
        dueDate: dueDate ? new Date(dueDate) : null,
        pregnancyStage,
        observations: notes || '',
        lastInteraction: new Date()
      };

      const updatedContact = await Contact.findByIdAndUpdate(
        req.params.id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedContact) {
        return res.status(404).json({
          error: 'Gestante não encontrada',
          code: 'NOT_FOUND'
        });
      }

      console.log('✅ Gestante atualizada com sucesso:', req.params.id);

      res.json(updatedContact);
    } catch (error: any) {
      console.error('❌ Erro ao atualizar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota simples para deletar contato (sem autenticação - compatibilidade)
  app.delete('/api/contacts/:id', async (req, res) => {
    try {
      console.log('🗑️ Requisição DELETE /api/contacts recebida (sem auth)');
      console.log('🗑️ ID:', req.params.id);

      // Soft delete - marcar como inativa
      const deletedContact = await Contact.findByIdAndUpdate(
        req.params.id,
        { isActive: false, lastInteraction: new Date() },
        { new: true }
      );

      if (!deletedContact) {
        return res.status(404).json({
          error: 'Gestante não encontrada',
          code: 'NOT_FOUND'
        });
      }

      console.log('✅ Gestante deletada com sucesso:', req.params.id);

      res.json({ message: 'Gestante deletada com sucesso' });
    } catch (error: any) {
      console.error('❌ Erro ao deletar gestante:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  });

  // Rota simples para analytics do dashboard (sem autenticação - compatibilidade)
  app.get('/api/analytics/dashboard', async (req, res) => {
    try {
      console.log('📊 Analytics do dashboard solicitado (sem auth)');

      // Buscar estatísticas dos contatos
      const totalContacts = await Contact.countDocuments({ isActive: true });
      const highRiskContacts = await Contact.countDocuments({ isActive: true, isHighRisk: true });
      const urgentContacts = await Contact.countDocuments({ isActive: true, priority: 'urgent' });

      // Estatísticas por trimestre
      const firstTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'first_trimester' });
      const secondTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'second_trimester' });
      const thirdTrimester = await Contact.countDocuments({ isActive: true, pregnancyStage: 'third_trimester' });
      const postpartum = await Contact.countDocuments({ isActive: true, pregnancyStage: 'postpartum' });

      // Estatísticas de mensagens
      const totalMessages = await Message.countDocuments();
      const todayMessages = await Message.countDocuments({
        timestamp: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
      });

      const analytics = {
        totalContacts,
        highRiskContacts,
        urgentContacts,
        pregnancyStages: {
          firstTrimester,
          secondTrimester,
          thirdTrimester,
          postpartum
        },
        messages: {
          total: totalMessages,
          today: todayMessages
        },
        lastUpdated: new Date().toISOString()
      };

      console.log(`📊 Analytics calculado - Gestantes encontradas: ${totalContacts}`);

      res.json(analytics);
    } catch (error: any) {
      console.error('❌ Erro ao calcular analytics:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para obter mensagens de um contato
  app.get('/api/contacts/:id/messages', async (req, res) => {
    try {
      const messages = await Message.find({ contact: req.params.id })
        .sort({ timestamp: -1 })
        .populate('contact');
      res.json(messages);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const response = await geminiService.generateResponse(contact, message);
      res.json(response);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para gerar mensagem de acompanhamento
  app.post('/api/ai/follow-up', async (req, res) => {
    try {
      const { contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const message = await geminiService.generateFollowUpMessage(contact);
      res.json({ message });
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota de debug para visualizar dados do banco
  app.get('/api/debug/database', async (req, res) => {
    try {
      console.log('🔍 Consultando dados do banco...');

      // Buscar todas as coleções
      const collections = await Contact.db.db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      console.log('📊 Coleções encontradas:', collectionNames);

      // Buscar dados de cada coleção
      const databaseData: any = {};

      for (const collectionName of collectionNames) {
        try {
          const collection = Contact.db.db.collection(collectionName);
          const count = await collection.countDocuments();
          const sampleData = await collection.find({}).limit(5).toArray();

          databaseData[collectionName] = {
            count,
            sampleData
          };

          console.log(`📋 ${collectionName}: ${count} documentos`);
        } catch (error: any) {
          console.error(`❌ Erro ao consultar ${collectionName}:`, error);
          databaseData[collectionName] = {
            error: error.message
          };
        }
      }

      res.json({
        message: 'Dados do banco de dados',
        database: process.env.MONGODB_URI ? 'MongoDB Atlas' : 'MongoDB Local',
        collections: collectionNames,
        data: databaseData,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      console.error('❌ Erro ao consultar banco:', error);
      res.status(500).json({
        error: 'Erro ao consultar banco de dados',
        code: 'DATABASE_ERROR',
        details: error.message
      });
    }
  });
}