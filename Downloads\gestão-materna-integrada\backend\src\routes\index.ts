import express from 'express';
import { WhatsAppClient } from '../services/whatsapp';
import { GeminiAIService } from '../services/gemini';
import { Contact } from '../models/Contact';
import { Message } from '../models/Message';
import authRoutes from './auth';
import contactRoutes from './contacts';
import messageRoutes from './messages';
import analyticsRoutes from './analytics';
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';

export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de contatos (gestantes)
  app.use('/api/contacts', contactRoutes);

  // Rotas de mensagens
  app.use('/api/messages', messageRoutes);

  // Rotas de analytics
  app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rota para enviar mensagem
  app.post('/api/whatsapp/send', async (req, res) => {
    try {
      const { to, message } = req.body;
      const response = await whatsappClient.sendMessage(to, message);
      res.json({ success: true, response });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota para enviar mensagens em massa
  app.post('/api/whatsapp/bulk', async (req, res) => {
    try {
      const { contacts, message } = req.body;
      const results = await whatsappClient.sendBulkMessages(contacts, message);
      res.json({ success: true, results });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Rota para listar contatos
  app.get('/api/contacts', async (req, res) => {
    try {
      const contacts = await Contact.find().sort({ lastInteraction: -1 });
      res.json(contacts);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para obter mensagens de um contato
  app.get('/api/contacts/:id/messages', async (req, res) => {
    try {
      const messages = await Message.find({ contact: req.params.id })
        .sort({ timestamp: -1 })
        .populate('contact');
      res.json(messages);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const response = await geminiService.generateResponse(contact, message);
      res.json(response);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para gerar mensagem de acompanhamento
  app.post('/api/ai/follow-up', async (req, res) => {
    try {
      const { contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      const contact = await Contact.findById(contactId);
      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      const message = await geminiService.generateFollowUpMessage(contact);
      res.json({ message });
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });
} 