import mongoose, { Schema, Document } from 'mongoose';

export interface IContact extends Document {
  phone: string;
  name: string;
  email?: string;
  pushname?: string;
  dateOfBirth?: Date;
  age?: number;

  // Dados da gestação
  pregnancyStage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  dueDate?: Date;
  gestationalWeeks?: number;
  isHighRisk?: boolean;
  complications?: string[];

  // Dados médicos
  bloodType?: string;
  allergies?: string[];
  medications?: string[];
  medicalHistory?: string;

  // Dados de contato
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Dados de acompanhamento
  lastInteraction: Date;
  nextAppointment?: Date;
  notes?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  // Dados do sistema
  isActive: boolean;
  createdBy?: mongoose.Types.ObjectId;
  assignedTo?: mongoose.Types.ObjectId;

  // Métodos
  getGestationalAge(): number | null;
  getPregnancyStatus(): string;
  isOverdue(): boolean;
}

const ContactSchema = new Schema({
  phone: {
    type: String,
    required: [true, 'Telefone é obrigatório'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Aceitar formato brasileiro: (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
        // Aceitar formato WhatsApp: <EMAIL>
        // Aceitar formato internacional: +55XXXXXXXXXXX
        const brazilianFormat = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
        const whatsappFormat = /^\d+@c\.us$/;
        const internationalFormat = /^\+?[\d\s\-\(\)]+$/;

        return brazilianFormat.test(v) || whatsappFormat.test(v) || internationalFormat.test(v);
      },
      message: 'Formato de telefone inválido. Use: (XX) XXXXX-XXXX ou formato WhatsApp'
    }
  },
  name: {
    type: String,
    required: [true, 'Nome é obrigatório'],
    trim: true,
    minlength: [2, 'Nome deve ter pelo menos 2 caracteres'],
    maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email inválido']
  },
  pushname: {
    type: String,
    trim: true
  },
  dateOfBirth: {
    type: Date,
    validate: {
      validator: function(v: Date) {
        return !v || v <= new Date();
      },
      message: 'Data de nascimento não pode ser no futuro'
    }
  },
  age: {
    type: Number,
    min: [10, 'Idade mínima é 10 anos'],
    max: [60, 'Idade máxima é 60 anos']
  },

  // Dados da gestação
  pregnancyStage: {
    type: String,
    enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
  },
  dueDate: {
    type: Date,
    validate: {
      validator: function(v: Date) {
        return !v || v >= new Date();
      },
      message: 'Data prevista do parto deve ser no futuro'
    }
  },
  gestationalWeeks: {
    type: Number,
    min: [0, 'Semanas gestacionais não podem ser negativas'],
    max: [45, 'Semanas gestacionais não podem exceder 45']
  },
  isHighRisk: {
    type: Boolean,
    default: false
  },
  complications: [{
    type: String,
    trim: true
  }],

  // Dados médicos
  bloodType: {
    type: String,
    enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
  },
  allergies: [{
    type: String,
    trim: true
  }],
  medications: [{
    type: String,
    trim: true
  }],
  medicalHistory: {
    type: String,
    maxlength: [1000, 'Histórico médico deve ter no máximo 1000 caracteres']
  },

  // Dados de contato
  emergencyContact: {
    name: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    relationship: {
      type: String,
      trim: true
    }
  },
  address: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    zipCode: {
      type: String,
      trim: true
    }
  },

  // Dados de acompanhamento
  lastInteraction: {
    type: Date,
    default: Date.now
  },
  nextAppointment: {
    type: Date
  },
  notes: {
    type: String,
    maxlength: [2000, 'Notas devem ter no máximo 2000 caracteres']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },

  // Dados do sistema
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Índices para performance (phone já tem índice único automático)
ContactSchema.index({ name: 1 });
ContactSchema.index({ email: 1 });
ContactSchema.index({ pregnancyStage: 1 });
ContactSchema.index({ dueDate: 1 });
ContactSchema.index({ isHighRisk: 1 });
ContactSchema.index({ priority: 1 });
ContactSchema.index({ isActive: 1 });
ContactSchema.index({ createdBy: 1 });
ContactSchema.index({ assignedTo: 1 });
ContactSchema.index({ lastInteraction: -1 });

// Middleware para calcular idade automaticamente
ContactSchema.pre('save', function(next) {
  if (this.dateOfBirth && !this.age) {
    const today = new Date();
    const birthDate = new Date(this.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    this.age = age;
  }
  next();
});

// Método para calcular idade gestacional
ContactSchema.methods.getGestationalAge = function(): number | null {
  if (!this.dueDate) return null;

  const today = new Date();
  const dueDate = new Date(this.dueDate);
  const gestationPeriod = 40 * 7; // 40 semanas em dias
  const daysSinceConception = gestationPeriod - Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  return Math.max(0, Math.floor(daysSinceConception / 7));
};

// Método para obter status da gravidez
ContactSchema.methods.getPregnancyStatus = function(): string {
  const weeks = this.getGestationalAge();
  if (!weeks) return 'Não informado';

  if (weeks < 13) return 'Primeiro trimestre';
  if (weeks < 27) return 'Segundo trimestre';
  if (weeks < 40) return 'Terceiro trimestre';
  return 'Pós-termo';
};

// Método para verificar se está atrasada
ContactSchema.methods.isOverdue = function(): boolean {
  if (!this.dueDate) return false;
  return new Date() > new Date(this.dueDate);
};

export const Contact = mongoose.model<IContact>('Contact', ContactSchema);