import mongoose, { Schema, Document } from 'mongoose';

export interface IContact extends Document {
  // Apenas 3 campos principais
  name: string;
  phone: string;
  babyGender?: 'male' | 'female' | 'unknown';

  // Campos de controle do sistema (mínimos necessários)
  isActive: boolean;
  lastInteraction: Date;
  createdBy?: mongoose.Types.ObjectId;

  // Método simplificado
  updateLastInteraction(): Promise<void>;
}

const ContactSchema = new Schema({
  name: {
    type: String,
    required: [true, 'Nome é obrigatório'],
    trim: true,
    minlength: [2, 'Nome deve ter pelo menos 2 caracteres'],
    maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
  },
  phone: {
    type: String,
    required: [true, 'Telefone é obrigatório'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Aceitar formato brasileiro: (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
        // Aceitar formato WhatsApp: <EMAIL>
        // Aceitar formato internacional: +55XXXXXXXXXXX
        const brazilianFormat = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
        const whatsappFormat = /^\d+@c\.us$/;
        const internationalFormat = /^\+?[\d\s\-\(\)]+$/;

        return brazilianFormat.test(v) || whatsappFormat.test(v) || internationalFormat.test(v);
      },
      message: 'Formato de telefone inválido. Use: (XX) XXXXX-XXXX ou formato WhatsApp'
    }
  },
  babyGender: {
    type: String,
    enum: ['male', 'female', 'unknown'],
    default: 'unknown'
  },

  // Campos de controle do sistema (mínimos necessários)
  isActive: {
    type: Boolean,
    default: true
  },
  lastInteraction: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Índices para performance (apenas os essenciais)
ContactSchema.index({ name: 1 });
ContactSchema.index({ phone: 1 }); // único automático
ContactSchema.index({ isActive: 1 });
ContactSchema.index({ lastInteraction: -1 });
ContactSchema.index({ babyGender: 1 });

// Método para atualizar última interação
ContactSchema.methods.updateLastInteraction = async function(): Promise<void> {
  this.lastInteraction = new Date();
  await this.save();
};

export const Contact = mongoose.model<IContact>('Contact', ContactSchema);