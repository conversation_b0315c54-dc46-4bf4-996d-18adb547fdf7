# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# WhatsApp tokens and sessions
backend/tokens/
backend/sessions/
backend/.wwebjs_auth/
backend/.wwebjs_cache/
tokens/
sessions/

# Database files
*.db
*.sqlite
*.sqlite3

# Build outputs
build/
.next/
.nuxt/

# Coverage
coverage/
*.lcov

# Temporary files
tmp/
temp/
.tmp/
