import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import { WhatsAppClient } from './services/whatsapp';
import { GeminiAIService } from './services/gemini';
import ProactiveSchedulerService from './services/proactiveScheduler';
import { setupRoutes } from './routes';
import { setupWebhooks } from './webhooks';
import { connectDatabase } from './config/database';

dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    methods: ['GET', 'POST']
  }
});

// Middlewares de segurança
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Rate limiting global
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // máximo 100 requests por IP
  message: {
    error: 'Muitas requisições deste IP, tente novamente mais tarde.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// CORS configurado
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing
app.use(express.json({
  limit: process.env.MAX_FILE_SIZE || '10mb',
  verify: (req, res, buf) => {
    // Verificação adicional de segurança para JSON
    try {
      JSON.parse(buf.toString());
    } catch (e) {
      throw new Error('JSON inválido');
    }
  }
}));

app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Inicialização dos serviços
const geminiService = new GeminiAIService();
const whatsappClient = new WhatsAppClient(io, geminiService);
const proactiveScheduler = new ProactiveSchedulerService(whatsappClient, geminiService);

// Configuração das rotas e webhooks
setupRoutes(app, whatsappClient, geminiService);
setupWebhooks(app, whatsappClient, geminiService);

// Conexão com o banco de dados e inicialização do scheduler
async function initializeServices() {
  await connectDatabase();

  console.log('🚀 Iniciando Proactive Scheduler...');
  await proactiveScheduler.start();
  console.log('✅ Todos os serviços inicializados com sucesso!');
}

initializeServices();

const PORT = process.env.PORT || 3000;

httpServer.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
  console.log('Aguardando conexão do WhatsApp...');
}); 