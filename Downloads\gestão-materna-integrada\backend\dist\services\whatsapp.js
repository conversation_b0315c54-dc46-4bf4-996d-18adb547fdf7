"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppClient = void 0;
const whatsapp_web_js_1 = require("whatsapp-web.js");
const qrcode_1 = __importDefault(require("qrcode"));
const Message_1 = require("../models/Message");
const Contact_1 = require("../models/Contact");
class WhatsAppClient {
    constructor(io, geminiService) {
        this.isConnected = false;
        this.io = io;
        this.geminiService = geminiService;
        this.client = new whatsapp_web_js_1.Client({
            authStrategy: new whatsapp_web_js_1.LocalAuth(),
            puppeteer: {
                args: ['--no-sandbox']
            }
        });
        this.initialize();
    }
    async initialize() {
        this.client.on('qr', async (qr) => {
            // Gerar QR Code como string para o console (compacto)
            const qrString = await qrcode_1.default.toString(qr, { type: 'terminal', small: true });
            console.log('\n=== QR CODE PARA CONEXÃO DO WHATSAPP ===\n');
            console.log(qrString);
            console.log('\n=======================================\n');
            // Enviar QR Code como imagem para o frontend
            const qrCode = await qrcode_1.default.toDataURL(qr);
            this.io.emit('qr', qrCode);
        });
        this.client.on('ready', () => {
            this.isConnected = true;
            console.log('\n✅ Cliente WhatsApp conectado com sucesso!\n');
            this.io.emit('status', 'connected');
            this.startFollowUpScheduler();
        });
        this.client.on('message', async (message) => {
            try {
                const contact = await this.getOrCreateContact(message.from);
                await this.saveMessage(message, contact);
                // Processar mensagem com IA
                const aiResponse = await this.geminiService.generateResponse(contact, message.body);
                // Enviar resposta
                await this.sendMessage(message.from, aiResponse.response);
                // Atualizar contato com informações da análise
                await this.updateContactWithAnalysis(contact, aiResponse);
                this.io.emit('message', {
                    from: message.from,
                    body: message.body,
                    timestamp: message.timestamp,
                    analysis: aiResponse
                });
            }
            catch (error) {
                console.error('Erro ao processar mensagem:', error);
                await this.sendMessage(message.from, 'Desculpe, tive um problema ao processar sua mensagem. Por favor, tente novamente em alguns instantes.');
            }
        });
        this.client.on('disconnected', () => {
            this.isConnected = false;
            console.log('\n❌ Cliente WhatsApp desconectado\n');
            this.io.emit('status', 'disconnected');
        });
        await this.client.initialize();
    }
    async getOrCreateContact(phone) {
        let contact = await Contact_1.Contact.findOne({ phone });
        if (!contact) {
            const info = await this.client.getContactById(phone);
            contact = await Contact_1.Contact.create({
                phone,
                name: info.name || phone,
                pushname: info.pushname,
                lastInteraction: new Date()
            });
        }
        return contact;
    }
    async saveMessage(message, contact) {
        await Message_1.Message.create({
            contact: contact._id,
            content: message.body,
            timestamp: message.timestamp,
            type: message.type,
            fromMe: message.fromMe
        });
    }
    async updateContactWithAnalysis(contact, analysis) {
        var _a, _b;
        await Contact_1.Contact.findByIdAndUpdate(contact._id, {
            lastInteraction: new Date(),
            pregnancyStage: ((_a = analysis.suggestions) === null || _a === void 0 ? void 0 : _a.includes('gestação')) ? 'em andamento' : contact.pregnancyStage,
            notes: ((_b = analysis.needs) === null || _b === void 0 ? void 0 : _b.length) ? `${contact.notes || ''}\nNecessidades: ${analysis.needs.join(', ')}` : contact.notes
        });
    }
    async sendMessage(to, message) {
        try {
            if (!this.isConnected) {
                throw new Error('WhatsApp não está conectado');
            }
            const response = await this.client.sendMessage(to, message);
            const contact = await this.getOrCreateContact(to);
            await this.saveMessage(response, contact);
            return response;
        }
        catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            throw error;
        }
    }
    async sendBulkMessages(contacts, message) {
        const results = await Promise.allSettled(contacts.map(contact => this.sendMessage(contact, message)));
        return results;
    }
    getStatus() {
        return {
            isConnected: this.isConnected,
            info: this.client.info
        };
    }
    async startFollowUpScheduler() {
        // Verificar contatos a cada 24 horas
        setInterval(async () => {
            try {
                const contacts = await Contact_1.Contact.find({
                    lastInteraction: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                });
                for (const contact of contacts) {
                    const followUpMessage = await this.geminiService.generateFollowUpMessage(contact);
                    await this.sendMessage(contact.phone, followUpMessage);
                }
            }
            catch (error) {
                console.error('Erro ao enviar mensagens de acompanhamento:', error);
            }
        }, 24 * 60 * 60 * 1000); // 24 horas
    }
}
exports.WhatsAppClient = WhatsAppClient;
