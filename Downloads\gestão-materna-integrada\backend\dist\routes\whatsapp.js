"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setWhatsAppClient = setWhatsAppClient;
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// Middleware para injetar o WhatsApp client
let whatsappClient;
function setWhatsAppClient(client) {
    whatsappClient = client;
}
// GET /api/whatsapp/status - Status da conexão WhatsApp
router.get('/status', auth_1.authenticate, async (req, res) => {
    try {
        const status = (whatsappClient === null || whatsappClient === void 0 ? void 0 : whatsappClient.getStatus()) || { isConnected: false, info: null };
        res.json({
            connected: status.isConnected,
            status: status.isConnected ? 'connected' : 'disconnected',
            clientInfo: status.info,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao obter status WhatsApp:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/whatsapp/qr - Obter QR Code para conexão
router.get('/qr', auth_1.authenticate, (0, auth_1.authorize)('manage:system'), async (req, res) => {
    try {
        if (!whatsappClient) {
            return res.status(503).json({
                error: 'WhatsApp client não inicializado',
                code: 'CLIENT_NOT_INITIALIZED'
            });
        }
        const status = whatsappClient.getStatus();
        if (status.isConnected) {
            return res.json({
                message: 'WhatsApp já está conectado',
                connected: true
            });
        }
        // Para obter QR code, seria necessário implementar um método específico
        // Por enquanto, retornamos uma mensagem informativa
        res.json({
            message: 'QR Code será exibido no console do servidor quando necessário',
            connected: false,
            instructions: 'Verifique o console do servidor para escanear o QR Code'
        });
    }
    catch (error) {
        console.error('Erro ao obter QR Code:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/whatsapp/send - Enviar mensagem
router.post('/send', auth_1.authenticate, (0, auth_1.authorize)('write:messages'), [
    (0, express_validator_1.body)('phone')
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Formato de telefone inválido'),
    (0, express_validator_1.body)('message')
        .trim()
        .isLength({ min: 1, max: 4000 })
        .withMessage('Mensagem deve ter entre 1 e 4000 caracteres'),
    (0, express_validator_1.body)('contactId')
        .optional()
        .isMongoId()
        .withMessage('ID do contato inválido')
], (0, auth_1.auditLog)('SEND_WHATSAPP_MESSAGE'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const status = whatsappClient === null || whatsappClient === void 0 ? void 0 : whatsappClient.getStatus();
        if (!whatsappClient || !(status === null || status === void 0 ? void 0 : status.isConnected)) {
            return res.status(503).json({
                error: 'WhatsApp não está conectado',
                code: 'WHATSAPP_NOT_CONNECTED'
            });
        }
        const { phone, message, contactId } = req.body;
        // Verificar se contato existe (se fornecido)
        let contact = null;
        if (contactId) {
            contact = await Contact_1.Contact.findById(contactId);
            if (!contact) {
                return res.status(404).json({
                    error: 'Contato não encontrado',
                    code: 'CONTACT_NOT_FOUND'
                });
            }
        }
        else {
            // Buscar contato pelo telefone
            contact = await Contact_1.Contact.findOne({ phone: phone.replace(/\D/g, '') });
        }
        // Enviar mensagem
        const success = await whatsappClient.sendMessage(phone, message);
        if (!success) {
            return res.status(500).json({
                error: 'Falha ao enviar mensagem',
                code: 'SEND_FAILED'
            });
        }
        res.json({
            message: 'Mensagem enviada com sucesso',
            phone,
            content: message,
            timestamp: new Date().toISOString(),
            contact: contact ? {
                id: contact._id,
                name: contact.name,
                phone: contact.phone
            } : null
        });
    }
    catch (error) {
        console.error('Erro ao enviar mensagem WhatsApp:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/whatsapp/send-bulk - Enviar mensagem em massa
router.post('/send-bulk', auth_1.authenticate, (0, auth_1.authorize)('send:bulk_messages'), [
    (0, express_validator_1.body)('contacts')
        .isArray({ min: 1, max: 100 })
        .withMessage('Lista de contatos deve ter entre 1 e 100 itens'),
    (0, express_validator_1.body)('contacts.*.phone')
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Formato de telefone inválido'),
    (0, express_validator_1.body)('message')
        .trim()
        .isLength({ min: 1, max: 4000 })
        .withMessage('Mensagem deve ter entre 1 e 4000 caracteres'),
    (0, express_validator_1.body)('delay')
        .optional()
        .isInt({ min: 1000, max: 60000 })
        .withMessage('Delay deve ser entre 1000ms e 60000ms')
], (0, auth_1.auditLog)('SEND_BULK_WHATSAPP_MESSAGES'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const status = whatsappClient === null || whatsappClient === void 0 ? void 0 : whatsappClient.getStatus();
        if (!whatsappClient || !(status === null || status === void 0 ? void 0 : status.isConnected)) {
            return res.status(503).json({
                error: 'WhatsApp não está conectado',
                code: 'WHATSAPP_NOT_CONNECTED'
            });
        }
        const { contacts, message, delay = 2000 } = req.body;
        const results = {
            total: contacts.length,
            sent: 0,
            failed: 0,
            details: []
        };
        // Enviar mensagens com delay
        for (let i = 0; i < contacts.length; i++) {
            const contact = contacts[i];
            try {
                const success = await whatsappClient.sendMessage(contact.phone, message);
                if (success) {
                    results.sent++;
                    results.details.push({
                        phone: contact.phone,
                        status: 'sent',
                        timestamp: new Date().toISOString()
                    });
                }
                else {
                    results.failed++;
                    results.details.push({
                        phone: contact.phone,
                        status: 'failed',
                        error: 'Falha no envio'
                    });
                }
            }
            catch (error) {
                results.failed++;
                results.details.push({
                    phone: contact.phone,
                    status: 'failed',
                    error: error instanceof Error ? error.message : 'Erro desconhecido'
                });
            }
            // Delay entre mensagens (exceto na última)
            if (i < contacts.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        res.json({
            message: 'Envio em massa concluído',
            results,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao enviar mensagens em massa:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/whatsapp/disconnect - Desconectar WhatsApp
router.post('/disconnect', auth_1.authenticate, (0, auth_1.authorize)('manage:system'), (0, auth_1.auditLog)('DISCONNECT_WHATSAPP'), async (req, res) => {
    try {
        if (!whatsappClient) {
            return res.status(404).json({
                error: 'WhatsApp client não encontrado',
                code: 'CLIENT_NOT_FOUND'
            });
        }
        // O método disconnect não está implementado no cliente atual
        // Por enquanto, retornamos uma mensagem informativa
        res.json({
            message: 'Funcionalidade de desconexão não implementada no cliente atual',
            timestamp: new Date().toISOString(),
            note: 'Reinicie o servidor para desconectar o WhatsApp'
        });
    }
    catch (error) {
        console.error('Erro ao desconectar WhatsApp:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/whatsapp/stats - Estatísticas do WhatsApp
router.get('/stats', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    var _a, _b;
    try {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const [totalMessages, todayMessages, sentMessages, receivedMessages, uniqueContacts] = await Promise.all([
            Message_1.Message.countDocuments(),
            Message_1.Message.countDocuments({ timestamp: { $gte: today } }),
            Message_1.Message.countDocuments({ fromMe: true }),
            Message_1.Message.countDocuments({ fromMe: false }),
            Message_1.Message.distinct('contact').then(contacts => contacts.length)
        ]);
        const stats = {
            connection: {
                status: ((_a = whatsappClient === null || whatsappClient === void 0 ? void 0 : whatsappClient.getStatus()) === null || _a === void 0 ? void 0 : _a.isConnected) ? 'connected' : 'disconnected',
                clientInfo: ((_b = whatsappClient === null || whatsappClient === void 0 ? void 0 : whatsappClient.getStatus()) === null || _b === void 0 ? void 0 : _b.info) || null
            },
            messages: {
                total: totalMessages,
                today: todayMessages,
                sent: sentMessages,
                received: receivedMessages
            },
            contacts: {
                unique: uniqueContacts
            },
            timestamp: new Date().toISOString()
        };
        res.json(stats);
    }
    catch (error) {
        console.error('Erro ao obter estatísticas WhatsApp:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
