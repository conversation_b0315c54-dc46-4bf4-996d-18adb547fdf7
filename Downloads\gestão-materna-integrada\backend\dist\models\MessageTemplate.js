"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageTemplate = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const MessageTemplateSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        maxlength: [100, 'Nome deve ter no máximo 100 caracteres']
    },
    category: {
        type: String,
        enum: ['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up'],
        required: true,
        index: true
    },
    // Conteúdo do template
    title: {
        type: String,
        required: true,
        maxlength: [200, 'Título deve ter no máximo 200 caracteres']
    },
    content: {
        type: String,
        required: true,
        maxlength: [2000, 'Conteúdo deve ter no máximo 2000 caracteres']
    },
    variables: [{
            type: String,
            trim: true
        }],
    // Condições de uso
    gestationalWeekMin: {
        type: Number,
        min: 0,
        max: 45
    },
    gestationalWeekMax: {
        type: Number,
        min: 0,
        max: 45
    },
    pregnancyStage: {
        type: String,
        enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
    },
    isHighRisk: Boolean,
    // Configurações
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    requiresResponse: {
        type: Boolean,
        default: false
    },
    followUpDays: {
        type: Number,
        min: 1,
        max: 30
    },
    // Metadados
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    usageCount: {
        type: Number,
        default: 0
    },
    successRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    // Dados do criador
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    lastModifiedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});
// Índices para performance
MessageTemplateSchema.index({ category: 1, isActive: 1 });
MessageTemplateSchema.index({ pregnancyStage: 1, isActive: 1 });
MessageTemplateSchema.index({ createdBy: 1 });
// Método para renderizar template com variáveis
MessageTemplateSchema.methods.render = function (variables) {
    let renderedContent = this.content;
    // Substituir variáveis no conteúdo
    for (const [key, value] of variables) {
        const placeholder = new RegExp(`{{${key}}}`, 'g');
        renderedContent = renderedContent.replace(placeholder, value);
    }
    return renderedContent;
};
// Método para incrementar uso
MessageTemplateSchema.methods.incrementUsage = async function () {
    this.usageCount += 1;
    await this.save();
};
// Método para atualizar taxa de sucesso
MessageTemplateSchema.methods.updateSuccessRate = async function (responded) {
    const totalResponses = this.usageCount;
    const currentSuccessCount = Math.round((this.successRate / 100) * totalResponses);
    const newSuccessCount = responded ? currentSuccessCount + 1 : currentSuccessCount;
    this.successRate = totalResponses > 0 ? (newSuccessCount / totalResponses) * 100 : 0;
    await this.save();
};
exports.MessageTemplate = mongoose_1.default.model('MessageTemplate', MessageTemplateSchema);
