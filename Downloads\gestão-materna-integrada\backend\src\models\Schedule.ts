import mongoose, { Schema, Document } from 'mongoose';

export interface ISchedule extends Document {
  contact: mongoose.Types.ObjectId;
  type: 'routine_checkup' | 'milestone_message' | 'educational_content' | 'emotional_support' | 'reminder' | 'follow_up';
  
  // Configurações de agendamento
  scheduledFor: Date;
  frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'custom';
  customInterval?: number; // em dias
  
  // Conteúdo da mensagem
  title: string;
  message: string;
  messageType: 'text' | 'educational' | 'motivational' | 'reminder' | 'question';
  
  // Condições para envio
  gestationalWeekMin?: number;
  gestationalWeekMax?: number;
  pregnancyStage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  isHighRisk?: boolean;
  
  // Status e controle
  status: 'pending' | 'sent' | 'failed' | 'cancelled' | 'completed';
  sentAt?: Date;
  responseReceived?: boolean;
  responseContent?: string;
  
  // Configurações avançadas
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requiresResponse?: boolean;
  autoReschedule?: boolean;
  maxAttempts?: number;
  attempts: number;
  
  // Personalização
  personalized: boolean;
  variables?: Map<string, string>; // Para personalizar mensagens
  
  // Dados do criador
  createdBy: mongoose.Types.ObjectId;
  lastModifiedBy?: mongoose.Types.ObjectId;
  
  // Métodos
  shouldSend(): boolean;
  personalize(): string;
  markAsSent(): Promise<void>;
  reschedule(days: number): Promise<void>;
}

const ScheduleSchema = new Schema({
  contact: {
    type: Schema.Types.ObjectId,
    ref: 'Contact',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: ['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up'],
    required: true,
    index: true
  },
  
  // Configurações de agendamento
  scheduledFor: {
    type: Date,
    required: true,
    index: true
  },
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'biweekly', 'monthly', 'custom']
  },
  customInterval: {
    type: Number,
    min: 1,
    max: 365
  },
  
  // Conteúdo da mensagem
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Título deve ter no máximo 200 caracteres']
  },
  message: {
    type: String,
    required: true,
    maxlength: [2000, 'Mensagem deve ter no máximo 2000 caracteres']
  },
  messageType: {
    type: String,
    enum: ['text', 'educational', 'motivational', 'reminder', 'question'],
    default: 'text'
  },
  
  // Condições para envio
  gestationalWeekMin: {
    type: Number,
    min: 0,
    max: 45
  },
  gestationalWeekMax: {
    type: Number,
    min: 0,
    max: 45
  },
  pregnancyStage: {
    type: String,
    enum: ['first_trimester', 'second_trimester', 'third_trimester', 'postpartum']
  },
  isHighRisk: Boolean,
  
  // Status e controle
  status: {
    type: String,
    enum: ['pending', 'sent', 'failed', 'cancelled', 'completed'],
    default: 'pending',
    index: true
  },
  sentAt: Date,
  responseReceived: {
    type: Boolean,
    default: false
  },
  responseContent: String,
  
  // Configurações avançadas
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  requiresResponse: {
    type: Boolean,
    default: false
  },
  autoReschedule: {
    type: Boolean,
    default: false
  },
  maxAttempts: {
    type: Number,
    default: 3,
    min: 1,
    max: 10
  },
  attempts: {
    type: Number,
    default: 0
  },
  
  // Personalização
  personalized: {
    type: Boolean,
    default: true
  },
  variables: {
    type: Map,
    of: String
  },
  
  // Dados do criador
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Índices para performance
ScheduleSchema.index({ scheduledFor: 1, status: 1 });
ScheduleSchema.index({ contact: 1, type: 1 });
ScheduleSchema.index({ pregnancyStage: 1, status: 1 });
ScheduleSchema.index({ createdBy: 1 });

// Método para verificar se deve ser enviado
ScheduleSchema.methods.shouldSend = function(): boolean {
  const now = new Date();
  
  // Verificar se já passou da hora agendada
  if (this.scheduledFor > now) return false;
  
  // Verificar status
  if (this.status !== 'pending') return false;
  
  // Verificar tentativas máximas
  if (this.attempts >= this.maxAttempts) return false;
  
  return true;
};

// Método para personalizar mensagem
ScheduleSchema.methods.personalize = function(): string {
  if (!this.personalized || !this.variables) {
    return this.message;
  }
  
  let personalizedMessage = this.message;
  
  // Substituir variáveis na mensagem
  for (const [key, value] of this.variables) {
    const placeholder = `{{${key}}}`;
    personalizedMessage = personalizedMessage.replace(new RegExp(placeholder, 'g'), value);
  }
  
  return personalizedMessage;
};

// Método para marcar como enviado
ScheduleSchema.methods.markAsSent = async function(): Promise<void> {
  this.status = 'sent';
  this.sentAt = new Date();
  this.attempts += 1;
  await this.save();
};

// Método para reagendar
ScheduleSchema.methods.reschedule = async function(days: number): Promise<void> {
  const newDate = new Date(this.scheduledFor);
  newDate.setDate(newDate.getDate() + days);
  
  this.scheduledFor = newDate;
  this.status = 'pending';
  this.attempts = 0;
  await this.save();
};

// Middleware para validação de semanas gestacionais
ScheduleSchema.pre('save', function(next) {
  if (this.gestationalWeekMin && this.gestationalWeekMax) {
    if (this.gestationalWeekMin > this.gestationalWeekMax) {
      next(new Error('Semana mínima não pode ser maior que a máxima'));
      return;
    }
  }
  next();
});

export const Schedule = mongoose.model<ISchedule>('Schedule', ScheduleSchema);
