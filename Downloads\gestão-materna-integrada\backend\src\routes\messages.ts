import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { Message, IMessage } from '../models/Message';
import { Contact } from '../models/Contact';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();

// Validações
const messageValidation = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 4000 })
    .withMessage('Conteúdo deve ter entre 1 e 4000 caracteres'),
  body('contact')
    .isMongoId()
    .withMessage('ID do contato inválido'),
  body('type')
    .optional()
    .isIn(['text', 'image', 'audio', 'video', 'document', 'location', 'contact_card', 'sticker'])
    .withMessage('Tipo de mensagem inválido')
];

// GET /api/messages - Listar mensagens com filtros
router.get('/',
  authenticate,
  authorize('read:messages'),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('contact').optional().isMongoId(),
    query('fromMe').optional().isBoolean(),
    query('category').optional().isIn(['medical', 'emotional', 'administrative', 'emergency', 'routine']),
    query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    query('sentiment').optional().isIn(['positive', 'negative', 'neutral', 'urgent']),
    query('unprocessed').optional().isBoolean(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const contact = req.query.contact as string;
      const fromMe = req.query.fromMe === 'true';
      const category = req.query.category as string;
      const priority = req.query.priority as string;
      const sentiment = req.query.sentiment as string;
      const unprocessed = req.query.unprocessed === 'true';
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Construir filtros
      const filters: any = {};

      if (contact) filters.contact = contact;
      if (req.query.fromMe !== undefined) filters.fromMe = fromMe;
      if (category) filters.category = category;
      if (priority) filters.priority = priority;
      if (sentiment) filters['sentiment.type'] = sentiment;
      if (unprocessed) filters.processedBy = { $exists: false };

      // Filtro de data
      if (startDate || endDate) {
        filters.timestamp = {};
        if (startDate) filters.timestamp.$gte = new Date(startDate);
        if (endDate) filters.timestamp.$lte = new Date(endDate);
      }

      // Executar consulta
      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        Message.find(filters)
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limit)
          .populate('contact', 'name phone pregnancyStage priority')
          .populate('processedBy', 'name email')
          .lean(),
        Message.countDocuments(filters)
      ]);

      res.json({
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          contact,
          fromMe: req.query.fromMe !== undefined ? fromMe : undefined,
          category,
          priority,
          sentiment,
          unprocessed: req.query.unprocessed !== undefined ? unprocessed : undefined,
          startDate,
          endDate
        }
      });
    } catch (error) {
      console.error('Erro ao listar mensagens:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/messages/:id - Obter mensagem específica
router.get('/:id',
  authenticate,
  authorize('read:messages'),
  async (req: Request, res: Response) => {
    try {
      const message = await Message.findById(req.params.id)
        .populate('contact', 'name phone pregnancyStage priority')
        .populate('processedBy', 'name email');

      if (!message) {
        return res.status(404).json({
          error: 'Mensagem não encontrada',
          code: 'MESSAGE_NOT_FOUND'
        });
      }

      res.json({ message });
    } catch (error) {
      console.error('Erro ao obter mensagem:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/messages - Criar nova mensagem
router.post('/',
  authenticate,
  authorize('write:messages'),
  messageValidation,
  auditLog('CREATE_MESSAGE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se contato existe
      const contact = await Contact.findById(req.body.contact);
      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Criar mensagem
      const messageData = {
        ...req.body,
        fromMe: true, // Mensagens criadas via API são sempre "de nós"
        timestamp: new Date()
      };

      const message = new Message(messageData);

      // Analisar conteúdo automaticamente
      await message.analyzeContent();
      await message.generateSuggestions();

      await message.save();

      // Atualizar última interação do contato
      contact.lastInteraction = new Date();
      await contact.save();

      await message.populate('contact', 'name phone pregnancyStage priority');

      res.status(201).json({
        message: 'Mensagem criada com sucesso',
        data: message
      });
    } catch (error) {
      console.error('Erro ao criar mensagem:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/messages/:id/process - Marcar mensagem como processada
router.put('/:id/process',
  authenticate,
  authorize('write:messages'),
  [
    body('notes')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Notas devem ter no máximo 1000 caracteres')
  ],
  auditLog('PROCESS_MESSAGE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const message = await Message.findById(req.params.id);

      if (!message) {
        return res.status(404).json({
          error: 'Mensagem não encontrada',
          code: 'MESSAGE_NOT_FOUND'
        });
      }

      await message.markAsProcessed(req.user!._id as any, req.body.notes);

      await message.populate('contact', 'name phone pregnancyStage priority');
      await message.populate('processedBy', 'name email');

      res.json({
        message: 'Mensagem marcada como processada',
        data: message
      });
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/messages/:id/analyze - Re-analisar mensagem
router.post('/:id/analyze',
  authenticate,
  authorize('write:messages'),
  auditLog('ANALYZE_MESSAGE'),
  async (req: Request, res: Response) => {
    try {
      const message = await Message.findById(req.params.id);

      if (!message) {
        return res.status(404).json({
          error: 'Mensagem não encontrada',
          code: 'MESSAGE_NOT_FOUND'
        });
      }

      // Re-analisar conteúdo
      await message.analyzeContent();
      await message.generateSuggestions();
      await message.save();

      await message.populate('contact', 'name phone pregnancyStage priority');

      res.json({
        message: 'Mensagem re-analisada com sucesso',
        data: {
          sentiment: message.sentiment,
          category: message.category,
          priority: message.priority,
          tags: message.tags,
          suggestions: message.suggestions
        }
      });
    } catch (error) {
      console.error('Erro ao re-analisar mensagem:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

export default router;
