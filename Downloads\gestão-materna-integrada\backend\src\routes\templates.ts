import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { MessageTemplate, IMessageTemplate } from '../models/MessageTemplate';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();

// Validações
const templateValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Nome deve ter entre 1 e 100 caracteres'),
  body('category')
    .isIn(['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up'])
    .withMessage('Categoria inválida'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Título deve ter entre 1 e 200 caracteres'),
  body('content')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Conteúdo deve ter entre 1 e 2000 caracteres')
];

// GET /api/templates - Listar templates
router.get('/',
  authenticate,
  authorize('read:messages'),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('category').optional().isIn(['routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up']),
    query('isActive').optional().isBoolean(),
    query('search').optional().isLength({ max: 100 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const category = req.query.category as string;
      const isActive = req.query.isActive === 'true';
      const search = req.query.search as string;

      // Construir filtros
      const filters: any = {};

      if (category) filters.category = category;
      if (req.query.isActive !== undefined) filters.isActive = isActive;

      if (search) {
        filters.$or = [
          { name: { $regex: search, $options: 'i' } },
          { title: { $regex: search, $options: 'i' } },
          { content: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (page - 1) * limit;

      const [templates, total] = await Promise.all([
        MessageTemplate.find(filters)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate('createdBy', 'name email')
          .populate('lastModifiedBy', 'name email')
          .lean(),
        MessageTemplate.countDocuments(filters)
      ]);

      res.json({
        templates,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          category,
          isActive: req.query.isActive !== undefined ? isActive : undefined,
          search
        }
      });
    } catch (error) {
      console.error('Erro ao listar templates:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/templates/:id - Obter template específico
router.get('/:id',
  authenticate,
  authorize('read:messages'),
  async (req: Request, res: Response) => {
    try {
      const template = await MessageTemplate.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('lastModifiedBy', 'name email');

      if (!template) {
        return res.status(404).json({
          error: 'Template não encontrado',
          code: 'TEMPLATE_NOT_FOUND'
        });
      }

      res.json({ template });
    } catch (error) {
      console.error('Erro ao obter template:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/templates - Criar template
router.post('/',
  authenticate,
  authorize('write:messages'),
  templateValidation,
  auditLog('CREATE_TEMPLATE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se nome já existe
      const existingTemplate = await MessageTemplate.findOne({ name: req.body.name });
      if (existingTemplate) {
        return res.status(409).json({
          error: 'Nome do template já existe',
          code: 'TEMPLATE_NAME_EXISTS'
        });
      }

      // Extrair variáveis do conteúdo
      const variables = extractVariables(req.body.content);

      // Criar template
      const templateData = {
        ...req.body,
        variables,
        createdBy: req.user!._id,
        usageCount: 0,
        successRate: 0
      };

      const template = new MessageTemplate(templateData);
      await template.save();

      await template.populate('createdBy', 'name email');

      res.status(201).json({
        message: 'Template criado com sucesso',
        template
      });
    } catch (error) {
      console.error('Erro ao criar template:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/templates/:id - Atualizar template
router.put('/:id',
  authenticate,
  authorize('write:messages'),
  templateValidation,
  auditLog('UPDATE_TEMPLATE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const template = await MessageTemplate.findById(req.params.id);

      if (!template) {
        return res.status(404).json({
          error: 'Template não encontrado',
          code: 'TEMPLATE_NOT_FOUND'
        });
      }

      // Verificar se nome já existe (exceto o atual)
      if (req.body.name !== template.name) {
        const existingTemplate = await MessageTemplate.findOne({ 
          name: req.body.name,
          _id: { $ne: template._id }
        });
        if (existingTemplate) {
          return res.status(409).json({
            error: 'Nome do template já existe',
            code: 'TEMPLATE_NAME_EXISTS'
          });
        }
      }

      // Extrair variáveis do conteúdo
      const variables = extractVariables(req.body.content);

      // Atualizar template
      Object.assign(template, req.body);
      template.variables = variables;
      template.lastModifiedBy = req.user!._id as any;
      await template.save();

      await template.populate('createdBy', 'name email');
      await template.populate('lastModifiedBy', 'name email');

      res.json({
        message: 'Template atualizado com sucesso',
        template
      });
    } catch (error) {
      console.error('Erro ao atualizar template:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// DELETE /api/templates/:id - Desativar template
router.delete('/:id',
  authenticate,
  authorize('write:messages'),
  auditLog('DEACTIVATE_TEMPLATE'),
  async (req: Request, res: Response) => {
    try {
      const template = await MessageTemplate.findById(req.params.id);

      if (!template) {
        return res.status(404).json({
          error: 'Template não encontrado',
          code: 'TEMPLATE_NOT_FOUND'
        });
      }

      // Desativar template (não deletar para manter histórico)
      template.isActive = false;
      template.lastModifiedBy = req.user!._id as any;
      await template.save();

      res.json({
        message: 'Template desativado com sucesso'
      });
    } catch (error) {
      console.error('Erro ao desativar template:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/templates/:id/preview - Visualizar template renderizado
router.post('/:id/preview',
  authenticate,
  authorize('read:messages'),
  [
    body('variables')
      .optional()
      .isObject()
      .withMessage('Variáveis devem ser um objeto')
  ],
  async (req: Request, res: Response) => {
    try {
      const template = await MessageTemplate.findById(req.params.id);

      if (!template) {
        return res.status(404).json({
          error: 'Template não encontrado',
          code: 'TEMPLATE_NOT_FOUND'
        });
      }

      const variables = new Map<string, string>(Object.entries(req.body.variables || {}).map(([k, v]) => [k, String(v)]));
      
      // Adicionar variáveis padrão para preview
      if (!variables.has('name')) variables.set('name', 'Maria');
      if (!variables.has('week')) variables.set('week', '24');
      if (!variables.has('stage')) variables.set('stage', 'segundo trimestre');

      const renderedContent = template.render(variables);

      res.json({
        original: template.content,
        rendered: renderedContent,
        variables: Array.from(variables.entries()).reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {} as any),
        availableVariables: template.variables
      });
    } catch (error) {
      console.error('Erro ao visualizar template:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/templates/stats/usage - Estatísticas de uso dos templates
router.get('/stats/usage',
  authenticate,
  authorize('read:analytics'),
  async (req: Request, res: Response) => {
    try {
      const stats = await MessageTemplate.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$category',
            totalTemplates: { $sum: 1 },
            totalUsage: { $sum: '$usageCount' },
            avgSuccessRate: { $avg: '$successRate' }
          }
        },
        { $sort: { totalUsage: -1 } }
      ]);

      const topTemplates = await MessageTemplate.find({ isActive: true })
        .sort({ usageCount: -1 })
        .limit(10)
        .select('name category usageCount successRate')
        .lean();

      res.json({
        byCategory: stats,
        topTemplates
      });
    } catch (error) {
      console.error('Erro ao obter estatísticas de templates:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// Função auxiliar para extrair variáveis do conteúdo
function extractVariables(content: string): string[] {
  const regex = /\{\{(\w+)\}\}/g;
  const variables: string[] = [];
  let match;

  while ((match = regex.exec(content)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }

  return variables;
}

export default router;
