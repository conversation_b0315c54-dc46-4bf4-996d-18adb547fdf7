import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { Schedule, ISchedule } from '../models/Schedule';
import { MessageTemplate } from '../models/MessageTemplate';
import { Contact } from '../models/Contact';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();

// Validações
const scheduleValidation = [
  body('contact')
    .isMongoId()
    .withMessage('ID do contato inválido'),
  body('type')
    .isIn(['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up'])
    .withMessage('Tipo de agendamento inválido'),
  body('scheduledFor')
    .isISO8601()
    .withMessage('Data de agendamento inválida'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('<PERSON><PERSON><PERSON>lo deve ter entre 1 e 200 caracteres'),
  body('message')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Mensagem deve ter entre 1 e 2000 caracteres')
];

// GET /api/schedules - Listar agendamentos
router.get('/',
  authenticate,
  authorize('read:messages'),
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('contact').optional().isMongoId(),
    query('type').optional().isIn(['routine_checkup', 'milestone_message', 'educational_content', 'emotional_support', 'reminder', 'follow_up']),
    query('status').optional().isIn(['pending', 'sent', 'failed', 'cancelled', 'completed']),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const contact = req.query.contact as string;
      const type = req.query.type as string;
      const status = req.query.status as string;
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Construir filtros
      const filters: any = {};

      if (contact) filters.contact = contact;
      if (type) filters.type = type;
      if (status) filters.status = status;

      // Filtro de data
      if (startDate || endDate) {
        filters.scheduledFor = {};
        if (startDate) filters.scheduledFor.$gte = new Date(startDate);
        if (endDate) filters.scheduledFor.$lte = new Date(endDate);
      }

      // Filtro por usuário (não-admin vê apenas seus agendamentos)
      if (req.user!.role !== 'admin') {
        filters.createdBy = req.user!._id;
      }

      const skip = (page - 1) * limit;

      const [schedules, total] = await Promise.all([
        Schedule.find(filters)
          .sort({ scheduledFor: -1 })
          .skip(skip)
          .limit(limit)
          .populate('contact', 'name phone pregnancyStage priority')
          .populate('createdBy', 'name email')
          .lean(),
        Schedule.countDocuments(filters)
      ]);

      res.json({
        schedules,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          contact,
          type,
          status,
          startDate,
          endDate
        }
      });
    } catch (error) {
      console.error('Erro ao listar agendamentos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/schedules/:id - Obter agendamento específico
router.get('/:id',
  authenticate,
  authorize('read:messages'),
  async (req: Request, res: Response) => {
    try {
      const schedule = await Schedule.findById(req.params.id)
        .populate('contact', 'name phone pregnancyStage priority')
        .populate('createdBy', 'name email')
        .populate('lastModifiedBy', 'name email');

      if (!schedule) {
        return res.status(404).json({
          error: 'Agendamento não encontrado',
          code: 'SCHEDULE_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' && (schedule.createdBy as any)._id.toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      res.json({ schedule });
    } catch (error) {
      console.error('Erro ao obter agendamento:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/schedules - Criar agendamento
router.post('/',
  authenticate,
  authorize('write:messages'),
  scheduleValidation,
  auditLog('CREATE_SCHEDULE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se contato existe
      const contact = await Contact.findById(req.body.contact);
      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Criar agendamento
      const scheduleData = {
        ...req.body,
        createdBy: req.user!._id,
        status: 'pending',
        attempts: 0
      };

      const schedule = new Schedule(scheduleData);
      await schedule.save();

      await schedule.populate('contact', 'name phone pregnancyStage priority');
      await schedule.populate('createdBy', 'name email');

      res.status(201).json({
        message: 'Agendamento criado com sucesso',
        schedule
      });
    } catch (error) {
      console.error('Erro ao criar agendamento:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/schedules/:id - Atualizar agendamento
router.put('/:id',
  authenticate,
  authorize('write:messages'),
  scheduleValidation,
  auditLog('UPDATE_SCHEDULE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const schedule = await Schedule.findById(req.params.id);

      if (!schedule) {
        return res.status(404).json({
          error: 'Agendamento não encontrado',
          code: 'SCHEDULE_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' && (schedule.createdBy as any).toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      // Não permitir editar agendamentos já enviados
      if (schedule.status === 'sent' || schedule.status === 'completed') {
        return res.status(400).json({
          error: 'Não é possível editar agendamentos já enviados',
          code: 'SCHEDULE_ALREADY_SENT'
        });
      }

      // Atualizar agendamento
      Object.assign(schedule, req.body);
      schedule.lastModifiedBy = req.user!._id as any;
      await schedule.save();

      await schedule.populate('contact', 'name phone pregnancyStage priority');
      await schedule.populate('createdBy', 'name email');

      res.json({
        message: 'Agendamento atualizado com sucesso',
        schedule
      });
    } catch (error) {
      console.error('Erro ao atualizar agendamento:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// DELETE /api/schedules/:id - Cancelar agendamento
router.delete('/:id',
  authenticate,
  authorize('write:messages'),
  auditLog('CANCEL_SCHEDULE'),
  async (req: Request, res: Response) => {
    try {
      const schedule = await Schedule.findById(req.params.id);

      if (!schedule) {
        return res.status(404).json({
          error: 'Agendamento não encontrado',
          code: 'SCHEDULE_NOT_FOUND'
        });
      }

      // Verificar permissão
      if (req.user!.role !== 'admin' && (schedule.createdBy as any).toString() !== (req.user!._id as any).toString()) {
        return res.status(403).json({
          error: 'Acesso negado',
          code: 'ACCESS_DENIED'
        });
      }

      // Cancelar agendamento
      schedule.status = 'cancelled';
      schedule.lastModifiedBy = req.user!._id as any;
      await schedule.save();

      res.json({
        message: 'Agendamento cancelado com sucesso'
      });
    } catch (error) {
      console.error('Erro ao cancelar agendamento:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/schedules/bulk - Criar agendamentos em massa
router.post('/bulk',
  authenticate,
  authorize('send:bulk_messages'),
  [
    body('contacts')
      .isArray({ min: 1, max: 100 })
      .withMessage('Lista de contatos deve ter entre 1 e 100 itens'),
    body('template')
      .isMongoId()
      .withMessage('ID do template inválido'),
    body('scheduledFor')
      .isISO8601()
      .withMessage('Data de agendamento inválida')
  ],
  auditLog('CREATE_BULK_SCHEDULES'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { contacts, template: templateId, scheduledFor } = req.body;

      // Verificar se template existe
      const template = await MessageTemplate.findById(templateId);
      if (!template || !template.isActive) {
        return res.status(404).json({
          error: 'Template não encontrado ou inativo',
          code: 'TEMPLATE_NOT_FOUND'
        });
      }

      const results = {
        total: contacts.length,
        created: 0,
        failed: 0,
        details: [] as any[]
      };

      // Criar agendamentos
      for (const contactId of contacts) {
        try {
          const contact = await Contact.findById(contactId);
          if (!contact) {
            results.failed++;
            results.details.push({
              contactId,
              status: 'failed',
              error: 'Contato não encontrado'
            });
            continue;
          }

          const schedule = new Schedule({
            contact: contactId,
            type: template.category,
            scheduledFor: new Date(scheduledFor),
            title: template.title,
            message: template.content,
            messageType: 'text',
            priority: template.priority,
            requiresResponse: template.requiresResponse,
            personalized: true,
            createdBy: req.user!._id
          });

          await schedule.save();

          results.created++;
          results.details.push({
            contactId,
            scheduleId: schedule._id,
            status: 'created'
          });
        } catch (error) {
          results.failed++;
          results.details.push({
            contactId,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }
      }

      res.status(201).json({
        message: 'Agendamentos em massa criados',
        results
      });
    } catch (error) {
      console.error('Erro ao criar agendamentos em massa:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

export default router;
