"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("../config/database");
const MessageTemplate_1 = require("../models/MessageTemplate");
const User_1 = require("../models/User");
// Carregar variáveis de ambiente
dotenv_1.default.config();
const seedTemplates = async () => {
    try {
        await (0, database_1.connectDatabase)();
        console.log('🌱 Iniciando seed de templates de mensagens...');
        // Buscar usuário admin para ser o criador
        const adminUser = await User_1.User.findOne({ role: 'admin' });
        if (!adminUser) {
            console.error('❌ Usuário admin não encontrado. Execute o seed principal primeiro.');
            process.exit(1);
        }
        // Limpar templates existentes
        await MessageTemplate_1.MessageTemplate.deleteMany({});
        console.log('🗑️  Templates existentes removidos');
        // Templates de check-up de rotina
        const routineTemplates = [
            {
                name: 'Check-up Semanal - Primeiro Trimestre',
                category: 'routine_checkup',
                title: 'Como você está se sentindo?',
                content: 'Olá {{name}}! Como você está se sentindo nesta {{week}}ª semana de gestação? 💙\n\nTem algum sintoma novo ou preocupação que gostaria de compartilhar?\n\n• Enjoos\n• Cansaço\n• Dores\n• Outras preocupações\n\nEstou aqui para ajudar! 🤗',
                gestationalWeekMin: 1,
                gestationalWeekMax: 13,
                pregnancyStage: 'first_trimester',
                priority: 'medium',
                requiresResponse: true,
                followUpDays: 3
            },
            {
                name: 'Check-up Quinzenal - Segundo Trimestre',
                category: 'routine_checkup',
                title: 'Acompanhamento quinzenal',
                content: 'Oi {{name}}! Tudo bem com você e o bebê? 👶\n\nVocê está na {{week}}ª semana, no {{stage}}. Como tem sido essa fase?\n\n• Já sente os movimentos do bebê?\n• Como está o apetite?\n• Algum desconforto?\n• Dúvidas sobre exames?\n\nConte-me como você está! 💕',
                gestationalWeekMin: 14,
                gestationalWeekMax: 27,
                pregnancyStage: 'second_trimester',
                priority: 'medium',
                requiresResponse: true,
                followUpDays: 3
            },
            {
                name: 'Check-up Semanal - Terceiro Trimestre',
                category: 'routine_checkup',
                title: 'Acompanhamento semanal - reta final',
                content: 'Olá {{name}}! Como você está na reta final? 🌟\n\nVocê está na {{week}}ª semana, faltam aproximadamente {{daysUntilDue}} dias para o parto!\n\n• Como estão os movimentos do bebê?\n• Tem sentido contrações?\n• Como está o sono?\n• Preparativos para o parto?\n\nEstamos quase lá! 💪',
                gestationalWeekMin: 28,
                gestationalWeekMax: 42,
                pregnancyStage: 'third_trimester',
                priority: 'high',
                requiresResponse: true,
                followUpDays: 2
            }
        ];
        // Templates de marcos da gestação
        const milestoneTemplates = [
            {
                name: 'Marco - 12 Semanas',
                category: 'milestone',
                title: 'Parabéns! Fim do primeiro trimestre',
                content: '🎉 Parabéns {{name}}! Você completou 12 semanas de gestação!\n\nO primeiro trimestre chegou ao fim e agora vem uma fase mais tranquila:\n\n✨ Menor risco de aborto\n✨ Enjoos tendem a diminuir\n✨ Mais energia\n✨ Pode contar a novidade para todos!\n\nComo você está se sentindo com essa conquista? 💙',
                gestationalWeekMin: 12,
                gestationalWeekMax: 12,
                priority: 'medium',
                requiresResponse: true
            },
            {
                name: 'Marco - 20 Semanas',
                category: 'milestone',
                title: 'Metade da gestação!',
                content: '🎊 Que emoção {{name}}! Você está na metade da gestação!\n\n20 semanas já se passaram e outras 20 estão por vir. Nesta fase:\n\n👶 Já dá para sentir os movimentos\n🔍 Ultrassom morfológico revela detalhes\n💕 Vínculo com o bebê se fortalece\n🎀 Talvez já saiba o sexo!\n\nJá conseguiu sentir o bebê se mexendo? Como tem sido essa experiência? ✨',
                gestationalWeekMin: 20,
                gestationalWeekMax: 20,
                priority: 'medium',
                requiresResponse: true
            },
            {
                name: 'Marco - 28 Semanas',
                category: 'milestone',
                title: 'Bem-vinda ao terceiro trimestre!',
                content: '🌟 Bem-vinda ao terceiro trimestre, {{name}}!\n\nAgora é a reta final! Seu bebê já tem grandes chances de sobreviver se nascer agora:\n\n🧠 Cérebro em desenvolvimento acelerado\n👁️ Olhos se abrem e fecham\n🫁 Pulmões amadurecendo\n📏 Cerca de 37cm e 1kg\n\nComo estão os preparativos? Já pensou no enxoval e no parto? 💪',
                gestationalWeekMin: 28,
                gestationalWeekMax: 28,
                priority: 'high',
                requiresResponse: true
            },
            {
                name: 'Marco - 36 Semanas',
                category: 'milestone',
                title: 'Bebê quase pronto!',
                content: '💕 {{name}}, seu bebê já está quase pronto para nascer!\n\nNa 36ª semana, ele já é considerado "a termo precoce":\n\n✅ Órgãos praticamente maduros\n✅ Peso ideal para nascer\n✅ Posição para o parto\n✅ Você pode entrar em trabalho de parto a qualquer momento!\n\nComo você está se preparando emocionalmente? Tem algum medo ou ansiedade? 🤗',
                gestationalWeekMin: 36,
                gestationalWeekMax: 36,
                priority: 'high',
                requiresResponse: true
            }
        ];
        // Templates educacionais
        const educationalTemplates = [
            {
                name: 'Educacional - Alimentação na Gestação',
                category: 'educational',
                title: 'Dicas de alimentação saudável',
                content: '🥗 Dicas de alimentação para você, {{name}}!\n\nUma boa alimentação é fundamental para você e seu bebê:\n\n✅ Frutas e vegetais variados\n✅ Proteínas magras (peixe, frango, ovos)\n✅ Grãos integrais\n✅ Laticínios ricos em cálcio\n✅ Muito líquido (água!)\n\n❌ Evite: álcool, peixes crus, embutidos\n\nTem alguma dúvida sobre alimentação? 🤔',
                priority: 'low',
                requiresResponse: false
            },
            {
                name: 'Educacional - Exercícios na Gestação',
                category: 'educational',
                title: 'Exercícios seguros na gestação',
                content: '🏃‍♀️ {{name}}, que tal se exercitar de forma segura?\n\nExercícios na gestação trazem muitos benefícios:\n\n💪 Fortalece músculos para o parto\n❤️ Melhora circulação\n😊 Reduz estresse e ansiedade\n⚡ Aumenta energia\n\nExercícios seguros:\n• Caminhada\n• Natação\n• Yoga para gestantes\n• Pilates adaptado\n\nSempre com liberação médica! Você pratica alguma atividade? 🧘‍♀️',
                priority: 'low',
                requiresResponse: false
            },
            {
                name: 'Educacional - Sinais de Alerta',
                category: 'educational',
                title: 'Sinais de alerta importantes',
                content: '🚨 {{name}}, é importante conhecer os sinais de alerta:\n\nProcure ajuda médica imediatamente se tiver:\n\n🔴 Sangramento vaginal\n🔴 Dor abdominal intensa\n🔴 Dor de cabeça forte e persistente\n🔴 Visão embaçada\n🔴 Inchaço súbito\n🔴 Diminuição dos movimentos do bebê\n🔴 Febre alta\n🔴 Vômitos excessivos\n\nNa dúvida, sempre procure orientação! Sua saúde e a do bebê são prioridade! 💙',
                priority: 'high',
                requiresResponse: false
            }
        ];
        // Templates de suporte emocional
        const emotionalTemplates = [
            {
                name: 'Suporte - Ansiedade Gestacional',
                category: 'emotional_support',
                title: 'Lidando com a ansiedade',
                content: '💙 {{name}}, é normal sentir ansiedade na gestação.\n\nVocê não está sozinha! Muitas gestantes passam por isso:\n\n🌸 Medo do parto\n🌸 Preocupação com o bebê\n🌸 Mudanças no corpo\n🌸 Responsabilidades futuras\n\nDicas para relaxar:\n• Respiração profunda\n• Meditação\n• Conversar com outras mães\n• Atividades prazerosas\n\nComo você tem lidado com essas emoções? Quer conversar? 🤗',
                priority: 'high',
                requiresResponse: true,
                followUpDays: 2
            },
            {
                name: 'Suporte - Autoestima na Gestação',
                category: 'emotional_support',
                title: 'Você está linda!',
                content: '✨ {{name}}, você está radiante!\n\nSeu corpo está fazendo algo incrível - criando uma vida! 👶\n\nLembre-se:\n💕 Cada mudança tem um propósito\n💕 Você é forte e capaz\n💕 Seu bebê já te ama\n💕 Você será uma mãe maravilhosa\n\nAs mudanças físicas podem incomodar, mas são temporárias. O amor que você está gerando é para sempre! 🌟\n\nComo você está se sentindo com seu corpo? 💙',
                priority: 'medium',
                requiresResponse: true
            }
        ];
        // Templates de lembretes
        const reminderTemplates = [
            {
                name: 'Lembrete - Consulta Pré-natal',
                category: 'reminder',
                title: 'Lembrete de consulta',
                content: '📅 Oi {{name}}! Lembrete importante:\n\nSua consulta pré-natal está agendada para amanhã às {{time}}.\n\n📋 Não esqueça de levar:\n• Cartão da gestante\n• Exames anteriores\n• Lista de dúvidas\n• Carteirinha do convênio\n\nPrecisa remarcar ou tem alguma dúvida? Me avise! 😊',
                priority: 'high',
                requiresResponse: false
            },
            {
                name: 'Lembrete - Exames',
                category: 'reminder',
                title: 'Lembrete de exames',
                content: '🔬 {{name}}, lembrete dos seus exames:\n\n{{examType}} agendado para {{date}} às {{time}}.\n\n📝 Orientações:\n{{instructions}}\n\nLocal: {{location}}\n\nQualquer dúvida, estou aqui para ajudar! 💙',
                priority: 'high',
                requiresResponse: false
            }
        ];
        // Criar todos os templates
        const allTemplates = [
            ...routineTemplates,
            ...milestoneTemplates,
            ...educationalTemplates,
            ...emotionalTemplates,
            ...reminderTemplates
        ];
        for (const templateData of allTemplates) {
            // Extrair variáveis do conteúdo
            const variables = extractVariables(templateData.content);
            const template = new MessageTemplate_1.MessageTemplate({
                ...templateData,
                variables,
                createdBy: adminUser._id,
                isActive: true,
                usageCount: 0,
                successRate: 0
            });
            await template.save();
        }
        console.log(`✅ ${allTemplates.length} templates criados com sucesso!`);
        console.log('\n📋 Templates criados por categoria:');
        console.log(`   🔄 Check-ups de rotina: ${routineTemplates.length}`);
        console.log(`   🎯 Marcos da gestação: ${milestoneTemplates.length}`);
        console.log(`   📚 Educacionais: ${educationalTemplates.length}`);
        console.log(`   💙 Suporte emocional: ${emotionalTemplates.length}`);
        console.log(`   📅 Lembretes: ${reminderTemplates.length}`);
        console.log('\n🚀 Templates prontos para uso no sistema de agendamento proativo!');
        process.exit(0);
    }
    catch (error) {
        console.error('❌ Erro ao executar seed de templates:', error);
        process.exit(1);
    }
};
// Função auxiliar para extrair variáveis do conteúdo
function extractVariables(content) {
    const regex = /\{\{(\w+)\}\}/g;
    const variables = [];
    let match;
    while ((match = regex.exec(content)) !== null) {
        if (!variables.includes(match[1])) {
            variables.push(match[1]);
        }
    }
    return variables;
}
seedTemplates();
