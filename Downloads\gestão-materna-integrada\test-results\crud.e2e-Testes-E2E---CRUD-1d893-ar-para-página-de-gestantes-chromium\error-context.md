# Test info

- Name: Testes E2E - CRUD de Gestantes >> deve navegar para página de gestantes
- Location: C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:15:3

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('[data-testid="menu-gestantes"]')

    at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:17:16
```

# Page snapshot

```yaml
- text: <PERSON><PERSON>
- navigation:
  - link "Dashboard":
    - /url: "#/dashboard"
  - link "Gestantes":
    - /url: "#/gestantes"
  - link "WhatsApp & IA":
    - /url: "#/whatsapp"
  - link "Configurações":
    - /url: "#/configuracoes"
- paragraph: © 2025 Gestão Materna
- banner:
  - button:
    - img
  - text: API Key não configurada. Verifique as variáveis de ambiente.
  - img "User Avatar"
- main:
  - text: Network Error
  - button "Tentar Novamente"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | // Configuração base para testes E2E
   4 | const BASE_URL = 'http://localhost:5173';
   5 |
   6 | test.describe('Testes E2E - CRUD de Gestantes', () => {
   7 |   test.beforeEach(async ({ page }) => {
   8 |     // Navegar para a página principal
   9 |     await page.goto(BASE_URL);
   10 |     
   11 |     // Aguardar a página carregar completamente
   12 |     await page.waitForLoadState('networkidle');
   13 |   });
   14 |
   15 |   test('deve navegar para página de gestantes', async ({ page }) => {
   16 |     // Clicar no menu de gestantes
>  17 |     await page.click('[data-testid="menu-gestantes"]');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
   18 |     
   19 |     // Verificar se chegou na página correta
   20 |     await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
   21 |     await expect(page.locator('h1')).toContainText('Gerenciamento de Gestantes');
   22 |   });
   23 |
   24 |   test('deve listar gestantes existentes', async ({ page }) => {
   25 |     await page.goto(`${BASE_URL}/gestantes`);
   26 |     
   27 |     // Aguardar a tabela carregar
   28 |     await page.waitForSelector('[data-testid="pregnant-table"]');
   29 |     
   30 |     // Verificar se há gestantes na lista
   31 |     const rows = page.locator('[data-testid="pregnant-table"] tbody tr');
   32 |     await expect(rows).toHaveCount.greaterThan(0);
   33 |   });
   34 |
   35 |   test('deve criar nova gestante', async ({ page }) => {
   36 |     await page.goto(`${BASE_URL}/gestantes`);
   37 |     
   38 |     // Clicar no botão "Nova Gestante"
   39 |     await page.click('[data-testid="new-pregnant-button"]');
   40 |     
   41 |     // Aguardar modal abrir
   42 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
   43 |     
   44 |     // Preencher formulário
   45 |     await page.fill('[aria-label="Nome"]', 'Maria da Silva E2E');
   46 |     await page.fill('[aria-label="Telefone"]', '11999999999');
   47 |     await page.fill('[aria-label="Email"]', '<EMAIL>');
   48 |     
   49 |     // Submeter formulário
   50 |     await page.click('button[type="submit"]');
   51 |     
   52 |     // Verificar se gestante foi criada
   53 |     await expect(page.locator('text=Gestante cadastrada com sucesso!')).toBeVisible();
   54 |     
   55 |     // Verificar se aparece na lista
   56 |     await expect(page.locator('text=Maria da Silva E2E')).toBeVisible();
   57 |   });
   58 |
   59 |   test('deve editar gestante existente', async ({ page }) => {
   60 |     await page.goto(`${BASE_URL}/gestantes`);
   61 |     
   62 |     // Aguardar tabela carregar
   63 |     await page.waitForSelector('[data-testid="pregnant-table"]');
   64 |     
   65 |     // Clicar no primeiro botão de editar
   66 |     await page.click('[data-testid="pregnant-table"] button:has-text("Editar")');
   67 |     
   68 |     // Aguardar modal abrir
   69 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
   70 |     
   71 |     // Verificar se dados estão preenchidos
   72 |     const nameInput = page.locator('[aria-label="Nome"]');
   73 |     await expect(nameInput).not.toHaveValue('');
   74 |     
   75 |     // Alterar nome
   76 |     await nameInput.fill('Nome Editado E2E');
   77 |     
   78 |     // Salvar alterações
   79 |     await page.click('button[type="submit"]');
   80 |     
   81 |     // Verificar sucesso
   82 |     await expect(page.locator('text=Gestante atualizada com sucesso!')).toBeVisible();
   83 |     
   84 |     // Verificar se nome foi alterado na lista
   85 |     await expect(page.locator('text=Nome Editado E2E')).toBeVisible();
   86 |   });
   87 |
   88 |   test('deve excluir gestante', async ({ page }) => {
   89 |     await page.goto(`${BASE_URL}/gestantes`);
   90 |     
   91 |     // Aguardar tabela carregar
   92 |     await page.waitForSelector('[data-testid="pregnant-table"]');
   93 |     
   94 |     // Obter nome da primeira gestante
   95 |     const firstRowName = await page.locator('[data-testid="pregnant-table"] tbody tr:first-child td:first-child').textContent();
   96 |     
   97 |     // Clicar no botão excluir
   98 |     await page.click('[data-testid="pregnant-table"] tbody tr:first-child button:has-text("Excluir")');
   99 |     
  100 |     // Aguardar modal de confirmação
  101 |     await page.waitForSelector('[data-testid="delete-confirm-modal"]');
  102 |     
  103 |     // Verificar se nome aparece na confirmação
  104 |     await expect(page.locator(`text=${firstRowName}`)).toBeVisible();
  105 |     
  106 |     // Confirmar exclusão
  107 |     await page.click('button:has-text("Excluir")');
  108 |     
  109 |     // Verificar sucesso
  110 |     await expect(page.locator('text=Gestante removida com sucesso!')).toBeVisible();
  111 |     
  112 |     // Verificar se gestante foi removida da lista
  113 |     await expect(page.locator(`text=${firstRowName}`)).not.toBeVisible();
  114 |   });
  115 |
  116 |   test('deve buscar gestantes', async ({ page }) => {
  117 |     await page.goto(`${BASE_URL}/gestantes`);
```