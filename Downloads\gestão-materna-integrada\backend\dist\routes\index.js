"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRoutes = setupRoutes;
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
const auth_1 = __importDefault(require("./auth"));
const contacts_1 = __importDefault(require("./contacts"));
function setupRoutes(app, whatsappClient, geminiService) {
    // Rotas de autenticação
    app.use('/api/auth', auth_1.default);
    // Rotas de contatos (gestantes)
    app.use('/api/contacts', contacts_1.default);
    // Rota para obter status do WhatsApp
    app.get('/api/whatsapp/status', (req, res) => {
        res.json(whatsappClient.getStatus());
    });
    // Rota para enviar mensagem
    app.post('/api/whatsapp/send', async (req, res) => {
        try {
            const { to, message } = req.body;
            const response = await whatsappClient.sendMessage(to, message);
            res.json({ success: true, response });
        }
        catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });
    // Rota para enviar mensagens em massa
    app.post('/api/whatsapp/bulk', async (req, res) => {
        try {
            const { contacts, message } = req.body;
            const results = await whatsappClient.sendBulkMessages(contacts, message);
            res.json({ success: true, results });
        }
        catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });
    // Rota para listar contatos
    app.get('/api/contacts', async (req, res) => {
        try {
            const contacts = await Contact_1.Contact.find().sort({ lastInteraction: -1 });
            res.json(contacts);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para obter mensagens de um contato
    app.get('/api/contacts/:id/messages', async (req, res) => {
        try {
            const messages = await Message_1.Message.find({ contact: req.params.id })
                .sort({ timestamp: -1 })
                .populate('contact');
            res.json(messages);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para processar mensagem com IA
    app.post('/api/ai/process', async (req, res) => {
        try {
            const { message, contactId } = req.body;
            if (!contactId) {
                return res.status(400).json({ error: 'ID do contato é obrigatório' });
            }
            const contact = await Contact_1.Contact.findById(contactId);
            if (!contact) {
                return res.status(404).json({ error: 'Contato não encontrado' });
            }
            const response = await geminiService.generateResponse(contact, message);
            res.json(response);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para gerar mensagem de acompanhamento
    app.post('/api/ai/follow-up', async (req, res) => {
        try {
            const { contactId } = req.body;
            if (!contactId) {
                return res.status(400).json({ error: 'ID do contato é obrigatório' });
            }
            const contact = await Contact_1.Contact.findById(contactId);
            if (!contact) {
                return res.status(404).json({ error: 'Contato não encontrado' });
            }
            const message = await geminiService.generateFollowUpMessage(contact);
            res.json({ message });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
}
