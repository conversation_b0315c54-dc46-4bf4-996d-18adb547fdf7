"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRoutes = setupRoutes;
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
const auth_1 = __importDefault(require("./auth"));
const contacts_1 = __importDefault(require("./contacts"));
const messages_1 = __importDefault(require("./messages"));
const analytics_1 = __importDefault(require("./analytics"));
const whatsapp_1 = __importStar(require("./whatsapp"));
function setupRoutes(app, whatsappClient, geminiService) {
    // Configurar WhatsApp client nas rotas
    (0, whatsapp_1.setWhatsAppClient)(whatsappClient);
    // Rotas de autenticação
    app.use('/api/auth', auth_1.default);
    // Rotas de contatos (gestantes)
    app.use('/api/contacts', contacts_1.default);
    // Rotas de mensagens
    app.use('/api/messages', messages_1.default);
    // Rotas de analytics
    app.use('/api/analytics', analytics_1.default);
    // Rotas do WhatsApp
    app.use('/api/whatsapp', whatsapp_1.default);
    // Rota para enviar mensagem
    app.post('/api/whatsapp/send', async (req, res) => {
        try {
            const { to, message } = req.body;
            const response = await whatsappClient.sendMessage(to, message);
            res.json({ success: true, response });
        }
        catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });
    // Rota para enviar mensagens em massa
    app.post('/api/whatsapp/bulk', async (req, res) => {
        try {
            const { contacts, message } = req.body;
            const results = await whatsappClient.sendBulkMessages(contacts, message);
            res.json({ success: true, results });
        }
        catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });
    // Rota para listar contatos
    app.get('/api/contacts', async (req, res) => {
        try {
            const contacts = await Contact_1.Contact.find().sort({ lastInteraction: -1 });
            res.json(contacts);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para obter mensagens de um contato
    app.get('/api/contacts/:id/messages', async (req, res) => {
        try {
            const messages = await Message_1.Message.find({ contact: req.params.id })
                .sort({ timestamp: -1 })
                .populate('contact');
            res.json(messages);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para processar mensagem com IA
    app.post('/api/ai/process', async (req, res) => {
        try {
            const { message, contactId } = req.body;
            if (!contactId) {
                return res.status(400).json({ error: 'ID do contato é obrigatório' });
            }
            const contact = await Contact_1.Contact.findById(contactId);
            if (!contact) {
                return res.status(404).json({ error: 'Contato não encontrado' });
            }
            const response = await geminiService.generateResponse(contact, message);
            res.json(response);
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para gerar mensagem de acompanhamento
    app.post('/api/ai/follow-up', async (req, res) => {
        try {
            const { contactId } = req.body;
            if (!contactId) {
                return res.status(400).json({ error: 'ID do contato é obrigatório' });
            }
            const contact = await Contact_1.Contact.findById(contactId);
            if (!contact) {
                return res.status(404).json({ error: 'Contato não encontrado' });
            }
            const message = await geminiService.generateFollowUpMessage(contact);
            res.json({ message });
        }
        catch (error) {
            res.status(500).json({ error: error.message });
        }
    });
}
