import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import PregnantFormModal from '../../../components/pregnant/PregnantFormModal';
import { 
  PregnantWomanSchema, 
  CreatePregnantWomanSchema, 
  UpdatePregnantWomanSchema,
  safeValidate 
} from '../../schemas/validation';
import { sanitizeInput, isValidEmail, isValidBrazilianPhone } from '../../utils/security';

// Mock do toast
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Validação de Formulários CRUD', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Validação de Campos de Gestante', () => {
    it('deve validar nome obrigatório', () => {
      const invalidData = {
        id: '1',
        name: '', // Nome vazio
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve validar tamanho mínimo do nome', () => {
      const invalidData = {
        id: '1',
        name: 'A', // Muito curto
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });

    it('deve validar tamanho máximo do nome', () => {
      const invalidData = {
        id: '1',
        name: 'A'.repeat(101), // Muito longo
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });

    it('deve validar caracteres permitidos no nome', () => {
      const invalidData = {
        id: '1',
        name: 'Ana123Silva', // Números não permitidos
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });

    it('deve aceitar nomes com acentos', () => {
      const validData = {
        id: '1',
        name: 'Ana Lúcia da Silva',
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, validData);
      expect(result.success).toBe(true);
    });
  });

  describe('Validação de Telefone', () => {
    it('deve validar formato de telefone brasileiro', () => {
      const validPhones = [
        '11999999999',
        '(11) 99999-9999',
        '+55 11 99999-9999',
        '11 99999-9999',
      ];

      validPhones.forEach(phone => {
        expect(isValidBrazilianPhone(phone)).toBe(true);
      });
    });

    it('deve rejeitar telefones inválidos', () => {
      const invalidPhones = [
        '123',
        '999999999999999',
        'abc123',
        '11 9999-999', // Muito curto
      ];

      invalidPhones.forEach(phone => {
        expect(isValidBrazilianPhone(phone)).toBe(false);
      });
    });

    it('deve normalizar telefone no formulário', () => {
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      const phoneInput = screen.getByLabelText(/telefone/i);
      fireEvent.change(phoneInput, { target: { value: '(11) 99999-9999' } });

      // O valor deveria ser normalizado para apenas números
      expect(phoneInput.value).toBe('(11) 99999-9999');
    });
  });

  describe('Validação de Email', () => {
    it('deve validar emails corretos', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    it('deve rejeitar emails inválidos', () => {
      const invalidEmails = [
        'email-invalido',
        '@domain.com',
        'user@',
        'user@domain',
      ];

      invalidEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(false);
      });
    });

    it('deve permitir email vazio (campo opcional)', () => {
      const validData = {
        id: '1',
        name: 'Ana Silva',
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        email: '', // Email vazio
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, validData);
      expect(result.success).toBe(true);
    });
  });

  describe('Validação de Data Prevista', () => {
    it('deve aceitar datas futuras válidas', () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 6);

      const validData = {
        id: '1',
        name: 'Ana Silva',
        dueDate: futureDate.toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, validData);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar datas no passado', () => {
      const pastDate = new Date();
      pastDate.setMonth(pastDate.getMonth() - 1);

      const invalidData = {
        id: '1',
        name: 'Ana Silva',
        dueDate: pastDate.toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });

    it('deve rejeitar datas muito distantes no futuro', () => {
      const distantFuture = new Date();
      distantFuture.setFullYear(distantFuture.getFullYear() + 2);

      const invalidData = {
        id: '1',
        name: 'Ana Silva',
        dueDate: distantFuture.toISOString(),
        phone: '11999999999',
        createdAt: new Date().toISOString(),
      };

      const result = safeValidate(PregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('Validação de Idade', () => {
    it('deve aceitar idades válidas', () => {
      const validAges = [18, 25, 35, 45];

      validAges.forEach(age => {
        const validData = {
          id: '1',
          name: 'Ana Silva',
          dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
          phone: '11999999999',
          createdAt: new Date().toISOString(),
          age,
        };

        const result = safeValidate(PregnantWomanSchema, validData);
        expect(result.success).toBe(true);
      });
    });

    it('deve rejeitar idades inválidas', () => {
      const invalidAges = [10, 65, -5, 0];

      invalidAges.forEach(age => {
        const invalidData = {
          id: '1',
          name: 'Ana Silva',
          dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
          phone: '11999999999',
          createdAt: new Date().toISOString(),
          age,
        };

        const result = safeValidate(PregnantWomanSchema, invalidData);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('Sanitização de Dados', () => {
    it('deve sanitizar inputs maliciosos', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>Ana',
        'javascript:alert("hack")',
        'Ana<img src=x onerror=alert(1)>Silva',
      ];

      maliciousInputs.forEach(input => {
        const sanitized = sanitizeInput(input);
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('onerror');
      });
    });

    it('deve preservar caracteres seguros', () => {
      const safeInput = 'Ana Lúcia da Silva - Primeira gestação (28 anos)';
      const sanitized = sanitizeInput(safeInput);
      
      expect(sanitized).toContain('Ana Lúcia');
      expect(sanitized).toContain('Silva');
      expect(sanitized).toContain('28 anos');
    });

    it('deve limitar tamanho de strings muito longas', () => {
      const longInput = 'A'.repeat(15000);
      const sanitized = sanitizeInput(longInput);
      
      expect(sanitized.length).toBeLessThanOrEqual(10000);
    });
  });

  describe('Validação de Formulário Completo', () => {
    it('deve validar formulário de criação completo', async () => {
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      // Preencher todos os campos obrigatórios
      await userEvent.type(screen.getByLabelText(/nome/i), 'Ana Silva');
      await userEvent.type(screen.getByLabelText(/telefone/i), '11999999999');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');

      // Submeter formulário
      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Ana Silva',
          phone: '11999999999',
          email: '<EMAIL>',
        })
      );
    });

    it('deve impedir submissão com campos inválidos', async () => {
      const mockOnSave = vi.fn();

      renderWithRouter(
        <PregnantFormModal
          isOpen={true}
          onClose={vi.fn()}
          onSave={mockOnSave}
          contact={null}
        />
      );

      // Preencher com dados inválidos
      await userEvent.type(screen.getByLabelText(/nome/i), 'A'); // Muito curto
      await userEvent.type(screen.getByLabelText(/telefone/i), '123'); // Inválido
      await userEvent.type(screen.getByLabelText(/email/i), 'email-inválido');

      // Tentar submeter
      const saveButton = screen.getByText(/salvar/i);
      await userEvent.click(saveButton);

      // Não deveria chamar onSave devido à validação HTML5
      expect(mockOnSave).not.toHaveBeenCalled();
    });
  });

  describe('Schemas de Validação Específicos', () => {
    it('deve validar schema de criação (sem ID)', () => {
      const createData = {
        name: 'Ana Silva',
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
        email: '<EMAIL>',
      };

      const result = safeValidate(CreatePregnantWomanSchema, createData);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar ID no schema de criação', () => {
      const invalidCreateData = {
        id: '1', // Não deveria ter ID na criação
        name: 'Ana Silva',
        dueDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
        phone: '11999999999',
      };

      const result = safeValidate(CreatePregnantWomanSchema, invalidCreateData);
      expect(result.success).toBe(false);
    });

    it('deve validar schema de atualização (ID obrigatório)', () => {
      const updateData = {
        id: '1', // ID obrigatório para atualização
        name: 'Ana Silva Santos',
      };

      const result = safeValidate(UpdatePregnantWomanSchema, updateData);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar atualização sem ID', () => {
      const invalidUpdateData = {
        name: 'Ana Silva Santos',
        // ID ausente
      };

      const result = safeValidate(UpdatePregnantWomanSchema, invalidUpdateData);
      expect(result.success).toBe(false);
    });
  });
});
