"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const MessageSchema = new mongoose_1.Schema({
    contact: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Contact',
        required: true,
        index: true
    },
    content: {
        type: String,
        required: true,
        maxlength: [4000, 'Conteúdo da mensagem deve ter no máximo 4000 caracteres']
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    },
    type: {
        type: String,
        enum: ['text', 'image', 'audio', 'video', 'document', 'location', 'contact_card', 'sticker'],
        default: 'text'
    },
    fromMe: {
        type: Boolean,
        required: true,
        index: true
    },
    messageId: {
        type: String,
        unique: true,
        sparse: true
    },
    status: {
        type: String,
        enum: ['sent', 'delivered', 'read', 'failed'],
        default: 'sent'
    },
    // Dados do WhatsApp
    whatsappData: {
        id: String,
        chatId: String,
        quotedMsgId: String,
        isForwarded: Boolean,
        forwardingScore: Number
    },
    // Metadados de mídia
    mediaData: {
        filename: String,
        mimetype: String,
        filesize: Number,
        url: String,
        caption: String,
        duration: Number,
        width: Number,
        height: Number
    },
    // Análise de sentimento
    sentiment: {
        type: {
            type: String,
            enum: ['positive', 'negative', 'neutral', 'urgent']
        },
        score: {
            type: Number,
            min: 0,
            max: 1
        },
        confidence: {
            type: Number,
            min: 0,
            max: 1
        },
        keywords: [String]
    },
    // Necessidades e sugestões
    needs: [String],
    suggestions: [String],
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    // Categorização
    category: {
        type: String,
        enum: ['medical', 'emotional', 'administrative', 'emergency', 'routine']
    },
    tags: [String],
    // Resposta automática
    autoResponse: {
        triggered: Boolean,
        responseId: String,
        responseText: String,
        timestamp: Date
    },
    // Processamento
    processedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    },
    processedAt: Date,
    notes: {
        type: String,
        maxlength: [1000, 'Notas devem ter no máximo 1000 caracteres']
    }
}, {
    timestamps: true
});
// Índices para performance
MessageSchema.index({ contact: 1, timestamp: -1 });
MessageSchema.index({ fromMe: 1, timestamp: -1 });
MessageSchema.index({ status: 1 });
MessageSchema.index({ priority: 1 });
MessageSchema.index({ category: 1 });
MessageSchema.index({ 'sentiment.type': 1 });
MessageSchema.index({ processedBy: 1 });
// Método para analisar conteúdo com IA
MessageSchema.methods.analyzeContent = async function () {
    const content = this.content.toLowerCase();
    // Análise básica de sentimento
    const positiveWords = ['bem', 'ótimo', 'feliz', 'obrigada', 'melhor', 'bom', 'tranquila'];
    const negativeWords = ['dor', 'mal', 'preocupada', 'medo', 'ruim', 'problema', 'triste'];
    const urgentWords = ['urgente', 'emergência', 'sangramento', 'dor forte', 'hospital', 'socorro'];
    const medicalWords = ['consulta', 'médico', 'exame', 'sintoma', 'medicamento', 'tratamento'];
    const emotionalWords = ['ansiedade', 'nervosa', 'estresse', 'deprimida', 'sozinha'];
    let sentiment = 'neutral';
    let score = 0.5;
    let keywords = [];
    let category = 'routine';
    let priority = 'medium';
    // Análise de urgência
    if (urgentWords.some(word => content.includes(word))) {
        sentiment = 'urgent';
        score = 0.9;
        priority = 'urgent';
        category = 'emergency';
        keywords = urgentWords.filter(word => content.includes(word));
    }
    // Análise de sentimento negativo
    else if (negativeWords.some(word => content.includes(word))) {
        sentiment = 'negative';
        score = 0.3;
        priority = 'high';
        keywords = negativeWords.filter(word => content.includes(word));
    }
    // Análise de sentimento positivo
    else if (positiveWords.some(word => content.includes(word))) {
        sentiment = 'positive';
        score = 0.8;
        keywords = positiveWords.filter(word => content.includes(word));
    }
    // Categorização
    if (medicalWords.some(word => content.includes(word))) {
        category = 'medical';
    }
    else if (emotionalWords.some(word => content.includes(word))) {
        category = 'emotional';
        if (priority === 'medium')
            priority = 'high';
    }
    else if (content.includes('agendamento') || content.includes('horário') || content.includes('documento')) {
        category = 'administrative';
    }
    this.sentiment = {
        type: sentiment,
        score,
        confidence: 0.7,
        keywords
    };
    this.category = category;
    this.priority = priority;
    // Gerar tags automáticas
    const tags = [];
    if (content.includes('primeira vez'))
        tags.push('primeira-gestacao');
    if (content.includes('diabetes'))
        tags.push('diabetes');
    if (content.includes('pressão'))
        tags.push('hipertensao');
    if (content.includes('enjoo'))
        tags.push('enjoo');
    if (content.includes('movimento') || content.includes('mexer'))
        tags.push('movimentos-fetais');
    this.tags = tags;
};
// Método para gerar sugestões
MessageSchema.methods.generateSuggestions = async function () {
    var _a, _b;
    const suggestions = [];
    const content = this.content.toLowerCase();
    if (((_a = this.sentiment) === null || _a === void 0 ? void 0 : _a.type) === 'urgent') {
        suggestions.push('🚨 Entrar em contato imediatamente');
        suggestions.push('📞 Verificar necessidade de atendimento médico urgente');
        suggestions.push('🏥 Acionar protocolo de emergência');
        suggestions.push('📋 Documentar todos os sintomas relatados');
    }
    else if (((_b = this.sentiment) === null || _b === void 0 ? void 0 : _b.type) === 'negative') {
        suggestions.push('💙 Oferecer suporte emocional');
        suggestions.push('📅 Agendar consulta de acompanhamento');
        suggestions.push('📚 Fornecer informações tranquilizadoras');
        suggestions.push('🤝 Conectar com grupo de apoio');
    }
    else if (content.includes('dúvida') || content.includes('pergunta')) {
        suggestions.push('📖 Responder com informações educativas');
        suggestions.push('👨‍⚕️ Encaminhar para profissional especializado');
        suggestions.push('📱 Enviar material educativo');
    }
    if (this.category === 'medical') {
        suggestions.push('📝 Documentar sintomas no prontuário');
        suggestions.push('🔍 Verificar histórico médico');
        suggestions.push('📊 Acompanhar evolução dos sintomas');
    }
    else if (this.category === 'emotional') {
        suggestions.push('🧘‍♀️ Sugerir técnicas de relaxamento');
        suggestions.push('👥 Conectar com outras gestantes');
        suggestions.push('🎵 Recomendar atividades relaxantes');
    }
    else if (this.category === 'administrative') {
        suggestions.push('📋 Verificar documentação necessária');
        suggestions.push('⏰ Confirmar horários disponíveis');
        suggestions.push('📞 Entrar em contato para agendamento');
    }
    // Sugestões específicas por conteúdo
    if (content.includes('enjoo')) {
        suggestions.push('🍃 Sugerir chá de gengibre');
        suggestions.push('🍽️ Orientar sobre alimentação fracionada');
    }
    if (content.includes('movimento') || content.includes('mexer')) {
        suggestions.push('👶 Explicar sobre movimentos fetais normais');
        suggestions.push('📊 Orientar sobre contagem de movimentos');
    }
    this.suggestions = suggestions;
    return suggestions;
};
// Método para marcar como processada
MessageSchema.methods.markAsProcessed = async function (userId, notes) {
    this.processedBy = userId;
    this.processedAt = new Date();
    if (notes)
        this.notes = notes;
    await this.save();
};
exports.Message = mongoose_1.default.model('Message', MessageSchema);
