# Test info

- Name: Testes E2E - CRUD de Gestantes >> deve funcionar em dispositivos móveis
- Location: C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:203:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('h1')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('h1')

    at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:210:38
```

# Page snapshot

```yaml
- text: <PERSON><PERSON>
- navigation:
  - link "Dashboard":
    - /url: "#/dashboard"
  - link "Gestantes":
    - /url: "#/gestantes"
  - link "WhatsApp & IA":
    - /url: "#/whatsapp"
  - link "Configurações":
    - /url: "#/configuracoes"
- paragraph: © 2025 Gestão Materna
- banner:
  - button:
    - img
  - text: API Key não configurada. Verifique as variáveis de ambiente.
  - img "User Avatar"
- main:
  - text: Network Error
  - button "Tentar Novamente"
```

# Test source

```ts
  110 |     await expect(page.locator('text=Gestante removida com sucesso!')).toBeVisible();
  111 |     
  112 |     // Verificar se gestante foi removida da lista
  113 |     await expect(page.locator(`text=${firstRowName}`)).not.toBeVisible();
  114 |   });
  115 |
  116 |   test('deve buscar gestantes', async ({ page }) => {
  117 |     await page.goto(`${BASE_URL}/gestantes`);
  118 |     
  119 |     // Aguardar tabela carregar
  120 |     await page.waitForSelector('[data-testid="pregnant-table"]');
  121 |     
  122 |     // Obter total de linhas inicial
  123 |     const initialRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
  124 |     
  125 |     // Buscar por um nome específico
  126 |     await page.fill('[data-testid="search-input"]', 'Ana');
  127 |     
  128 |     // Aguardar filtro ser aplicado
  129 |     await page.waitForTimeout(500);
  130 |     
  131 |     // Verificar se resultados foram filtrados
  132 |     const filteredRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
  133 |     expect(filteredRows).toBeLessThanOrEqual(initialRows);
  134 |     
  135 |     // Verificar se todas as linhas visíveis contêm "Ana"
  136 |     const visibleNames = await page.locator('[data-testid="pregnant-table"] tbody tr td:first-child').allTextContents();
  137 |     visibleNames.forEach(name => {
  138 |       expect(name.toLowerCase()).toContain('ana');
  139 |     });
  140 |   });
  141 |
  142 |   test('deve validar campos obrigatórios', async ({ page }) => {
  143 |     await page.goto(`${BASE_URL}/gestantes`);
  144 |     
  145 |     // Abrir modal de criação
  146 |     await page.click('[data-testid="new-pregnant-button"]');
  147 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
  148 |     
  149 |     // Tentar submeter sem preencher campos
  150 |     await page.click('button[type="submit"]');
  151 |     
  152 |     // Verificar se validação HTML5 impede submissão
  153 |     const nameInput = page.locator('[aria-label="Nome"]');
  154 |     await expect(nameInput).toHaveAttribute('required');
  155 |     
  156 |     // Verificar se modal ainda está aberto (não foi submetido)
  157 |     await expect(page.locator('[data-testid="pregnant-form-modal"]')).toBeVisible();
  158 |   });
  159 |
  160 |   test('deve cancelar criação de gestante', async ({ page }) => {
  161 |     await page.goto(`${BASE_URL}/gestantes`);
  162 |     
  163 |     // Abrir modal
  164 |     await page.click('[data-testid="new-pregnant-button"]');
  165 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
  166 |     
  167 |     // Preencher alguns campos
  168 |     await page.fill('[aria-label="Nome"]', 'Teste Cancelar');
  169 |     
  170 |     // Cancelar
  171 |     await page.click('button:has-text("Cancelar")');
  172 |     
  173 |     // Verificar se modal fechou
  174 |     await expect(page.locator('[data-testid="pregnant-form-modal"]')).not.toBeVisible();
  175 |     
  176 |     // Verificar se gestante não foi criada
  177 |     await expect(page.locator('text=Teste Cancelar')).not.toBeVisible();
  178 |   });
  179 |
  180 |   test('deve exibir mensagens de erro para dados inválidos', async ({ page }) => {
  181 |     await page.goto(`${BASE_URL}/gestantes`);
  182 |     
  183 |     // Abrir modal
  184 |     await page.click('[data-testid="new-pregnant-button"]');
  185 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
  186 |     
  187 |     // Preencher com dados inválidos
  188 |     await page.fill('[aria-label="Nome"]', 'A'); // Muito curto
  189 |     await page.fill('[aria-label="Telefone"]', '123'); // Inválido
  190 |     await page.fill('[aria-label="Email"]', 'email-inválido'); // Formato inválido
  191 |     
  192 |     // Tentar submeter
  193 |     await page.click('button[type="submit"]');
  194 |     
  195 |     // Verificar se validação impede submissão
  196 |     const emailInput = page.locator('[aria-label="Email"]');
  197 |     await expect(emailInput).toHaveAttribute('type', 'email');
  198 |     
  199 |     // Modal deve permanecer aberto
  200 |     await expect(page.locator('[data-testid="pregnant-form-modal"]')).toBeVisible();
  201 |   });
  202 |
  203 |   test('deve funcionar em dispositivos móveis', async ({ page }) => {
  204 |     // Simular viewport mobile
  205 |     await page.setViewportSize({ width: 375, height: 667 });
  206 |     
  207 |     await page.goto(`${BASE_URL}/gestantes`);
  208 |     
  209 |     // Verificar se página é responsiva
> 210 |     await expect(page.locator('h1')).toBeVisible();
      |                                      ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  211 |     
  212 |     // Verificar se tabela é scrollável horizontalmente
  213 |     const table = page.locator('[data-testid="pregnant-table"]');
  214 |     await expect(table).toBeVisible();
  215 |     
  216 |     // Testar criação em mobile
  217 |     await page.click('[data-testid="new-pregnant-button"]');
  218 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
  219 |     
  220 |     // Modal deve ser visível e utilizável
  221 |     await expect(page.locator('[aria-label="Nome"]')).toBeVisible();
  222 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
  223 |   });
  224 |
  225 |   test('deve manter estado durante navegação', async ({ page }) => {
  226 |     await page.goto(`${BASE_URL}/gestantes`);
  227 |     
  228 |     // Fazer uma busca
  229 |     await page.fill('[data-testid="search-input"]', 'Ana');
  230 |     await page.waitForTimeout(500);
  231 |     
  232 |     // Navegar para outra página
  233 |     await page.click('[data-testid="menu-dashboard"]');
  234 |     await expect(page).toHaveURL(`${BASE_URL}/dashboard`);
  235 |     
  236 |     // Voltar para gestantes
  237 |     await page.click('[data-testid="menu-gestantes"]');
  238 |     await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
  239 |     
  240 |     // Verificar se busca foi mantida (ou limpa, dependendo do comportamento esperado)
  241 |     const searchValue = await page.locator('[data-testid="search-input"]').inputValue();
  242 |     // Pode ser '' (limpo) ou 'Ana' (mantido) - depende da implementação
  243 |     expect(typeof searchValue).toBe('string');
  244 |   });
  245 | });
  246 |
```