"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const whatsapp_1 = require("./services/whatsapp");
const gemini_1 = require("./services/gemini");
const routes_1 = require("./routes");
const webhooks_1 = require("./webhooks");
const database_1 = require("./config/database");
dotenv_1.default.config();
const app = (0, express_1.default)();
const httpServer = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(httpServer, {
    cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:5173',
        methods: ['GET', 'POST']
    }
});
// Middlewares de segurança
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false
}));
// Rate limiting global
const limiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // máximo 100 requests por IP
    message: {
        error: 'Muitas requisições deste IP, tente novamente mais tarde.',
        code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
// CORS configurado
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// Body parsing
app.use(express_1.default.json({
    limit: process.env.MAX_FILE_SIZE || '10mb',
    verify: (req, res, buf) => {
        // Verificação adicional de segurança para JSON
        try {
            JSON.parse(buf.toString());
        }
        catch (e) {
            throw new Error('JSON inválido');
        }
    }
}));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Inicialização dos serviços
const geminiService = new gemini_1.GeminiAIService();
const whatsappClient = new whatsapp_1.WhatsAppClient(io, geminiService);
// Configuração das rotas e webhooks
(0, routes_1.setupRoutes)(app, whatsappClient, geminiService);
(0, webhooks_1.setupWebhooks)(app, whatsappClient, geminiService);
// Conexão com o banco de dados
(0, database_1.connectDatabase)();
const PORT = process.env.PORT || 3000;
httpServer.listen(PORT, () => {
    console.log(`Servidor rodando na porta ${PORT}`);
    console.log('Aguardando conexão do WhatsApp...');
});
