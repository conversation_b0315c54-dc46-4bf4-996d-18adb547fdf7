import express, { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { User, IUser } from '../models/User';
import { authenticate, requireAdmin, auditLog } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting para rotas de autenticação
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 tentativas por IP
  message: {
    error: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
    code: 'TOO_MANY_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validações
const registerValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Senha deve ter pelo menos 8 caracteres')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial'),
  body('role')
    .optional()
    .isIn(['admin', 'nurse', 'doctor', 'coordinator'])
    .withMessage('Role inválido')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .notEmpty()
    .withMessage('Senha é obrigatória')
];

// POST /api/auth/register - Registrar novo usuário
router.post('/register', 
  requireAdmin,
  registerValidation,
  auditLog('USER_REGISTER'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { name, email, password, role } = req.body;

      // Verificar se usuário já existe
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(409).json({
          error: 'Email já está em uso',
          code: 'EMAIL_ALREADY_EXISTS'
        });
      }

      // Criar usuário
      const user = new User({
        name,
        email,
        password,
        role: role || 'nurse'
      });

      await user.save();

      res.status(201).json({
        message: 'Usuário criado com sucesso',
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          permissions: user.permissions,
          createdAt: user.createdAt
        }
      });
    } catch (error: any) {
      console.error('Erro ao registrar usuário:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/auth/login - Login
router.post('/login',
  authLimiter,
  loginValidation,
  auditLog('USER_LOGIN'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { email, password } = req.body;

      // Buscar usuário e verificar credenciais
      const user = await (User as any).findByCredentials(email, password);
      const token = user.generateAuthToken();

      res.json({
        message: 'Login realizado com sucesso',
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          permissions: user.permissions,
          lastLogin: user.lastLogin
        }
      });
    } catch (error: any) {
      console.error('Erro ao fazer login:', error);

      if (error.message === 'Credenciais inválidas') {
        return res.status(401).json({
          error: 'Email ou senha incorretos',
          code: 'INVALID_CREDENTIALS'
        });
      }

      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/auth/me - Obter dados do usuário atual
router.get('/me', authenticate, async (req: Request, res: Response) => {
  try {
    res.json({
      user: {
        id: req.user!._id,
        name: req.user!.name,
        email: req.user!.email,
        role: req.user!.role,
        permissions: req.user!.permissions,
        lastLogin: req.user!.lastLogin,
        createdAt: req.user!.createdAt
      }
    });
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// PUT /api/auth/profile - Atualizar perfil do usuário
router.put('/profile',
  authenticate,
  [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Nome deve ter entre 2 e 100 caracteres'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Email inválido')
  ],
  auditLog('PROFILE_UPDATE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { name, email } = req.body;
      const updates: any = {};

      if (name) updates.name = name;
      if (email) {
        // Verificar se email já está em uso
        const existingUser = await User.findOne({
          email,
          _id: { $ne: req.user!._id }
        });

        if (existingUser) {
          return res.status(409).json({
            error: 'Email já está em uso',
            code: 'EMAIL_ALREADY_EXISTS'
          });
        }

        updates.email = email;
      }

      const user = await User.findByIdAndUpdate(
        req.user!._id,
        updates,
        { new: true, runValidators: true }
      );

      res.json({
        message: 'Perfil atualizado com sucesso',
        user: {
          id: user!._id,
          name: user!.name,
          email: user!.email,
          role: user!.role,
          permissions: user!.permissions
        }
      });
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/auth/password - Alterar senha
router.put('/password',
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Senha atual é obrigatória'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('Nova senha deve ter pelo menos 8 caracteres')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Nova senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial')
  ],
  auditLog('PASSWORD_CHANGE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;

      // Buscar usuário com senha
      const user = await User.findById(req.user!._id).select('+password');
      if (!user) {
        return res.status(404).json({
          error: 'Usuário não encontrado',
          code: 'USER_NOT_FOUND'
        });
      }

      // Verificar senha atual
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          error: 'Senha atual incorreta',
          code: 'INVALID_CURRENT_PASSWORD'
        });
      }

      // Atualizar senha
      user.password = newPassword;
      await user.save();

      res.json({
        message: 'Senha alterada com sucesso'
      });
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/auth/logout - Logout (invalidar token no frontend)
router.post('/logout', authenticate, auditLog('USER_LOGOUT'), (req, res) => {
  res.json({
    message: 'Logout realizado com sucesso'
  });
});

export default router;
