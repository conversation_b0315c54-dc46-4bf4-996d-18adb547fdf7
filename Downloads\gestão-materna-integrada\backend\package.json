{"name": "gestacao-materna-backend", "version": "1.0.0", "description": "Backend para sistema de gestão materna integrada com WhatsApp e IA", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "seed": "ts-node src/scripts/seed.ts", "seed:templates": "ts-node src/scripts/seedTemplates.ts"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@tailwindcss/postcss": "^4.1.8", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "qrcode": "^1.5.3", "socket.io": "^4.7.2", "ts-node": "^10.9.2", "whatsapp-web.js": "^1.23.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}