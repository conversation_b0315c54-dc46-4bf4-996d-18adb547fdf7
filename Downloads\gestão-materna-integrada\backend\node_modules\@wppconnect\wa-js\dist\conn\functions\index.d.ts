/*!
 * Copyright 2021 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { genLinkDeviceCodeForPhoneNumber } from './genLinkDeviceCodeForPhoneNumber';
export { getAuthCode } from './getAuthCode';
export { getHistorySyncProgress, HistorySyncProgress, } from './getHistorySyncProgress';
export { getMyDeviceId } from './getMyDeviceId';
export { getMyUserId } from './getMyUserId';
export { getPlatform } from './getPlatform';
export { isAuthenticated } from './isAuthenticated';
export { isIdle } from './isIdle';
export { isMainInit } from './isMainInit';
export { isMainLoaded } from './isMainLoaded';
export { isMainReady } from './isMainReady';
export { isMultiDevice } from './isMultiDevice';
export { isOnline } from './isOnline';
export { isRegistered } from './isRegistered';
export { joinWebBeta } from './joinWebBeta';
export { logout } from './logout';
export { markAvailable, markUnavailable } from './markAvailable';
export { needsUpdate } from './needsUpdate';
export { refreshQR } from './refreshQR';
export { setKeepAlive } from './setKeepAlive';
export { setLimit } from './setLimit';
export { setMultiDevice } from './setMultiDevice';
