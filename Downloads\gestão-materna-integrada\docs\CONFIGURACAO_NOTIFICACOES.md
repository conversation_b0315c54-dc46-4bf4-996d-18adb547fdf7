# 📢 Configuração de Notificações CI/CD

Este documento explica como configurar as notificações automáticas para Slack, Discord e Microsoft Teams no pipeline CI/CD.

---

## 🔧 **Configuração de Secrets**

Para que as notificações funcionem, você precisa configurar os seguintes secrets no GitHub:

### **1. Slack**
1. Acesse o Slack e vá para **Apps** → **Incoming Webhooks**
2. Crie um novo webhook para o canal `#gestao-materna`
3. Copie a URL do webhook
4. No GitHub, vá para **Settings** → **Secrets and variables** → **Actions**
5. Adicione o secret:
   - **Nome**: `SLACK_WEBHOOK_URL`
   - **Valor**: URL do webhook do Slack

### **2. Discord**
1. No Discord, vá para **Server Settings** → **Integrations** → **Webhooks**
2. Crie um novo webhook para o canal desejado
3. Copie a URL do webhook
4. No GitHub, adicione o secret:
   - **Nome**: `DISCORD_WEBHOOK`
   - **Valor**: URL do webhook do Discord

### **3. Microsoft Teams**
1. No Teams, vá para o canal desejado
2. Clique em **...** → **Connectors** → **Incoming Webhook**
3. Configure o webhook e copie a URL
4. No GitHub, adicione o secret:
   - **Nome**: `TEAMS_WEBHOOK_URL`
   - **Valor**: URL do webhook do Teams

---

## 📋 **Tipos de Notificação**

### **✅ Notificações de Sucesso**
Enviadas quando:
- Deploy para staging é bem-sucedido
- Deploy para production é bem-sucedido
- Todos os testes passam
- Quality gates são atendidos

**Conteúdo inclui:**
- Nome do projeto
- Branch deployada
- Ambiente (staging/production)
- Autor do commit
- Status dos testes
- Coverage de código
- Resultados de security scan
- Link para detalhes

### **❌ Notificações de Falha**
Enviadas quando:
- Testes unitários falham
- Testes E2E falham
- Security scan detecta vulnerabilidades
- Quality gates não são atendidos

**Conteúdo inclui:**
- Detalhes da falha
- Quais etapas falharam
- Autor responsável
- Link para logs
- Ações necessárias

---

## 🎨 **Personalização das Mensagens**

### **Slack**
```yaml
- name: Notify Slack - Success
  uses: 8398a7/action-slack@v3
  with:
    status: success
    channel: '#gestao-materna'
    username: 'GitHub Actions'
    icon_emoji: ':rocket:'
    title: '🎉 Deploy Realizado com Sucesso!'
```

### **Discord**
```yaml
- name: Notify Discord - Success
  uses: Ilshidur/action-discord@master
  with:
    args: |
      🎉 **Deploy Realizado com Sucesso!**
      
      **Projeto:** Gestão Materna Integrada
      **Branch:** ${{ github.ref_name }}
```

### **Microsoft Teams**
```yaml
- name: Notify Teams - Success
  uses: skitionek/notify-microsoft-teams@master
  with:
    webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    overwrite: |
      {
        "@type": "MessageCard",
        "themeColor": "28a745",
        "title": "🎉 Deploy Realizado com Sucesso!"
      }
```

---

## 🔄 **Triggers das Notificações**

### **Quando são Enviadas**
- **Sempre** após completion dos jobs principais
- **Condicionalmente** baseado no resultado:
  - Sucesso: Deploy staging OU production bem-sucedido
  - Falha: Qualquer job principal falhou

### **Dependências**
```yaml
notification:
  needs: [deploy-staging, deploy-production, quality-gate]
  if: always()
```

---

## 📊 **Informações Incluídas**

### **Metadados do Build**
- **Projeto**: Gestão Materna Integrada
- **Branch**: Nome da branch
- **Commit**: SHA do commit
- **Autor**: Usuário que fez o push
- **Ambiente**: Staging ou Production
- **Timestamp**: Horário do deploy

### **Resultados dos Testes**
- **Testes Unitários**: Status e quantidade
- **Testes E2E**: Status e cobertura
- **Security Scan**: Vulnerabilidades encontradas
- **Coverage**: Porcentagem de cobertura
- **Quality Gates**: Status geral

### **Links Úteis**
- **Detalhes**: Link para a execução no GitHub Actions
- **Logs**: Acesso direto aos logs de falha
- **Commit**: Link para o commit específico

---

## 🛠️ **Configuração Avançada**

### **Canais Específicos**
Você pode configurar canais diferentes para diferentes tipos de notificação:

```yaml
# Para sucessos
channel: '#deployments'

# Para falhas
channel: '#alerts'
```

### **Menções Específicas**
Para mencionar pessoas ou grupos em falhas:

```yaml
text: |
  ❌ Pipeline falhou!
  <@user_id> <@team_id> por favor verificar
```

### **Horários de Notificação**
Para evitar spam, você pode configurar horários:

```yaml
if: |
  always() && 
  (github.event_name == 'push' || github.event_name == 'pull_request') &&
  contains(fromJSON('["08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18"]'), format('{0:HH}', github.event.head_commit.timestamp))
```

---

## 🧪 **Testando as Notificações**

### **1. Teste Manual**
1. Faça um push para a branch `develop`
2. Verifique se o pipeline executa
3. Confirme se as notificações chegam nos canais

### **2. Teste de Falha**
1. Introduza um erro proposital no código
2. Faça push e verifique notificações de falha
3. Corrija o erro e verifique notificações de sucesso

### **3. Validação dos Webhooks**
```bash
# Teste Slack
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"Teste de webhook Slack"}' \
  YOUR_SLACK_WEBHOOK_URL

# Teste Discord
curl -X POST -H 'Content-type: application/json' \
  --data '{"content":"Teste de webhook Discord"}' \
  YOUR_DISCORD_WEBHOOK_URL
```

---

## 🔒 **Segurança**

### **Proteção dos Webhooks**
- ✅ **Nunca** commite URLs de webhook no código
- ✅ **Sempre** use GitHub Secrets
- ✅ **Rotacione** webhooks periodicamente
- ✅ **Monitore** uso dos webhooks

### **Permissões**
- ✅ Webhooks têm acesso **apenas** aos canais configurados
- ✅ **Não** podem ler mensagens existentes
- ✅ **Não** têm acesso a dados sensíveis

---

## 📝 **Troubleshooting**

### **Notificações não chegam**
1. ✅ Verificar se secrets estão configurados
2. ✅ Validar URLs dos webhooks
3. ✅ Confirmar permissões nos canais
4. ✅ Checar logs do GitHub Actions

### **Mensagens malformadas**
1. ✅ Validar sintaxe YAML
2. ✅ Testar templates de mensagem
3. ✅ Verificar caracteres especiais
4. ✅ Confirmar encoding UTF-8

### **Falhas intermitentes**
1. ✅ Verificar rate limits dos serviços
2. ✅ Implementar retry logic
3. ✅ Monitorar status dos webhooks
4. ✅ Configurar fallbacks

---

## 🎯 **Próximos Passos**

### **Melhorias Futuras**
1. **Rich Cards**: Mensagens mais visuais
2. **Métricas**: Gráficos de performance
3. **Alertas Inteligentes**: Baseados em padrões
4. **Integração**: Jira, Trello, etc.

### **Monitoramento**
1. **Dashboard**: Status das notificações
2. **Analytics**: Frequência de falhas
3. **Alertas**: Webhooks offline
4. **Relatórios**: Resumos semanais

---

## ✅ **Checklist de Configuração**

- [ ] Secrets configurados no GitHub
- [ ] Webhooks testados manualmente
- [ ] Canais criados nos serviços
- [ ] Permissões configuradas
- [ ] Pipeline testado com sucesso
- [ ] Pipeline testado com falha
- [ ] Documentação atualizada
- [ ] Equipe treinada

**Configuração completa!** 🎉
