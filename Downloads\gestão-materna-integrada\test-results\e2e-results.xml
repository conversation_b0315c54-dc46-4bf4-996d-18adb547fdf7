<testsuites id="" name="" tests="77" failures="35" skipped="42" errors="0" time="125.86758600000002">
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="chromium" tests="11" failures="11" skipped="0" time="317.031" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="30.269">
<failure message="crud.e2e.test.ts:15:3 deve navegar para página de gestantes" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:15:3 › Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="menu-gestantes"]')


      15 |   test('deve navegar para página de gestantes', async ({ page }) => {
      16 |     // Clicar no menu de gestantes
    > 17 |     await page.click('[data-testid="menu-gestantes"]');
         |                ^
      18 |     
      19 |     // Verificar se chegou na página correta
      20 |     await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:17:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="30.172">
<failure message="crud.e2e.test.ts:24:3 deve listar gestantes existentes" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:24:3 › Testes E2E - CRUD de Gestantes › deve listar gestantes existentes 

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      26 |     
      27 |     // Aguardar a tabela carregar
    > 28 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      29 |     
      30 |     // Verificar se há gestantes na lista
      31 |     const rows = page.locator('[data-testid="pregnant-table"] tbody tr');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:28:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="30.191">
<failure message="crud.e2e.test.ts:35:3 deve criar nova gestante" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:35:3 › Testes E2E - CRUD de Gestantes › deve criar nova gestante ───

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      37 |     
      38 |     // Clicar no botão "Nova Gestante"
    > 39 |     await page.click('[data-testid="new-pregnant-button"]');
         |                ^
      40 |     
      41 |     // Aguardar modal abrir
      42 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:39:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="30.185">
<failure message="crud.e2e.test.ts:59:3 deve editar gestante existente" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:59:3 › Testes E2E - CRUD de Gestantes › deve editar gestante existente 

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      61 |     
      62 |     // Aguardar tabela carregar
    > 63 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      64 |     
      65 |     // Clicar no primeiro botão de editar
      66 |     await page.click('[data-testid="pregnant-table"] button:has-text("Editar")');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:63:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="30.19">
<failure message="crud.e2e.test.ts:88:3 deve excluir gestante" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:88:3 › Testes E2E - CRUD de Gestantes › deve excluir gestante ──────

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      90 |     
      91 |     // Aguardar tabela carregar
    > 92 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      93 |     
      94 |     // Obter nome da primeira gestante
      95 |     const firstRowName = await page.locator('[data-testid="pregnant-table"] tbody tr:first-child td:first-child').textContent();
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:92:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="30.174">
<failure message="crud.e2e.test.ts:116:3 deve buscar gestantes" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:116:3 › Testes E2E - CRUD de Gestantes › deve buscar gestantes ─────

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      118 |     
      119 |     // Aguardar tabela carregar
    > 120 |     await page.waitForSelector('[data-testid="pregnant-table"]');
          |                ^
      121 |     
      122 |     // Obter total de linhas inicial
      123 |     const initialRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:120:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="30.773">
<failure message="crud.e2e.test.ts:142:3 deve validar campos obrigatórios" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:142:3 › Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      144 |     
      145 |     // Abrir modal de criação
    > 146 |     await page.click('[data-testid="new-pregnant-button"]');
          |                ^
      147 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      148 |     
      149 |     // Tentar submeter sem preencher campos
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:146:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="30.991">
<failure message="crud.e2e.test.ts:160:3 deve cancelar criação de gestante" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:160:3 › Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      162 |     
      163 |     // Abrir modal
    > 164 |     await page.click('[data-testid="new-pregnant-button"]');
          |                ^
      165 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      166 |     
      167 |     // Preencher alguns campos
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:164:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="30.95">
<failure message="crud.e2e.test.ts:180:3 deve exibir mensagens de erro para dados inválidos" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:180:3 › Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      182 |     
      183 |     // Abrir modal
    > 184 |     await page.click('[data-testid="new-pregnant-button"]');
          |                ^
      185 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      186 |     
      187 |     // Preencher com dados inválidos
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:184:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="12.211">
<failure message="crud.e2e.test.ts:203:3 deve funcionar em dispositivos móveis" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:203:3 › Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('h1')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('h1')


      208 |     
      209 |     // Verificar se página é responsiva
    > 210 |     await expect(page.locator('h1')).toBeVisible();
          |                                      ^
      211 |     
      212 |     // Verificar se tabela é scrollável horizontalmente
      213 |     const table = page.locator('[data-testid="pregnant-table"]');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:210:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="30.925">
<failure message="crud.e2e.test.ts:225:3 deve manter estado durante navegação" type="FAILURE">
<![CDATA[  [chromium] › crud.e2e.test.ts:225:3 › Testes E2E - CRUD de Gestantes › deve manter estado durante navegação 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      227 |     
      228 |     // Fazer uma busca
    > 229 |     await page.fill('[data-testid="search-input"]', 'Ana');
          |                ^
      230 |     await page.waitForTimeout(500);
      231 |     
      232 |     // Navegar para outra página
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:229:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="firefox" tests="11" failures="9" skipped="2" time="305.154" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="30.15">
<failure message="crud.e2e.test.ts:15:3 deve navegar para página de gestantes" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:15:3 › Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="menu-gestantes"]')


      15 |   test('deve navegar para página de gestantes', async ({ page }) => {
      16 |     // Clicar no menu de gestantes
    > 17 |     await page.click('[data-testid="menu-gestantes"]');
         |                ^
      18 |     
      19 |     // Verificar se chegou na página correta
      20 |     await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:17:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="30.093">
<failure message="crud.e2e.test.ts:24:3 deve listar gestantes existentes" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:24:3 › Testes E2E - CRUD de Gestantes › deve listar gestantes existentes 

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      26 |     
      27 |     // Aguardar a tabela carregar
    > 28 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      29 |     
      30 |     // Verificar se há gestantes na lista
      31 |     const rows = page.locator('[data-testid="pregnant-table"] tbody tr');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:28:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="30.077">
<failure message="crud.e2e.test.ts:35:3 deve criar nova gestante" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:35:3 › Testes E2E - CRUD de Gestantes › deve criar nova gestante ────

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      37 |     
      38 |     // Clicar no botão "Nova Gestante"
    > 39 |     await page.click('[data-testid="new-pregnant-button"]');
         |                ^
      40 |     
      41 |     // Aguardar modal abrir
      42 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:39:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="30.079">
<failure message="crud.e2e.test.ts:59:3 deve editar gestante existente" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:59:3 › Testes E2E - CRUD de Gestantes › deve editar gestante existente 

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      61 |     
      62 |     // Aguardar tabela carregar
    > 63 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      64 |     
      65 |     // Clicar no primeiro botão de editar
      66 |     await page.click('[data-testid="pregnant-table"] button:has-text("Editar")');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:63:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="30.092">
<failure message="crud.e2e.test.ts:88:3 deve excluir gestante" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:88:3 › Testes E2E - CRUD de Gestantes › deve excluir gestante ───────

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      90 |     
      91 |     // Aguardar tabela carregar
    > 92 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      93 |     
      94 |     // Obter nome da primeira gestante
      95 |     const firstRowName = await page.locator('[data-testid="pregnant-table"] tbody tr:first-child td:first-child').textContent();
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:92:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="30.08">
<failure message="crud.e2e.test.ts:116:3 deve buscar gestantes" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:116:3 › Testes E2E - CRUD de Gestantes › deve buscar gestantes ──────

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      118 |     
      119 |     // Aguardar tabela carregar
    > 120 |     await page.waitForSelector('[data-testid="pregnant-table"]');
          |                ^
      121 |     
      122 |     // Obter total de linhas inicial
      123 |     const initialRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:120:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="30.104">
<failure message="crud.e2e.test.ts:142:3 deve validar campos obrigatórios" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:142:3 › Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      144 |     
      145 |     // Abrir modal de criação
    > 146 |     await page.click('[data-testid="new-pregnant-button"]');
          |                ^
      147 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      148 |     
      149 |     // Tentar submeter sem preencher campos
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:146:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="30.093">
<failure message="crud.e2e.test.ts:160:3 deve cancelar criação de gestante" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:160:3 › Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      162 |     
      163 |     // Abrir modal
    > 164 |     await page.click('[data-testid="new-pregnant-button"]');
          |                ^
      165 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      166 |     
      167 |     // Preencher alguns campos
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:164:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="25.309">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="15.263">
<failure message="crud.e2e.test.ts:203:3 deve funcionar em dispositivos móveis" type="FAILURE">
<![CDATA[  [firefox] › crud.e2e.test.ts:203:3 › Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('h1')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('h1')


      208 |     
      209 |     // Verificar se página é responsiva
    > 210 |     await expect(page.locator('h1')).toBeVisible();
          |                                      ^
      211 |     
      212 |     // Verificar se tabela é scrollável horizontalmente
      213 |     const table = page.locator('[data-testid="pregnant-table"]');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:210:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\test-failed-1.png]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\video.webm]]

[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="23.814">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="webkit" tests="11" failures="11" skipped="0" time="6.531" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="0.968">
<failure message="crud.e2e.test.ts:15:3 deve navegar para página de gestantes" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:15:3 › Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-22\0b824a5ac4cf6a8f45452175437a1298.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="0.524">
<failure message="crud.e2e.test.ts:24:3 deve listar gestantes existentes" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:24:3 › Testes E2E - CRUD de Gestantes › deve listar gestantes existentes 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-23\0057dcdb10f5841d50cdf3ede92537cd.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="0.657">
<failure message="crud.e2e.test.ts:35:3 deve criar nova gestante" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:35:3 › Testes E2E - CRUD de Gestantes › deve criar nova gestante ─────

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-24\bb96dca59228228612eab6f95fd85e90.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="0.712">
<failure message="crud.e2e.test.ts:59:3 deve editar gestante existente" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:59:3 › Testes E2E - CRUD de Gestantes › deve editar gestante existente 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-25\984738d79a61c187d4454c9824a9285b.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="0.524">
<failure message="crud.e2e.test.ts:88:3 deve excluir gestante" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:88:3 › Testes E2E - CRUD de Gestantes › deve excluir gestante ────────

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-26\f6325d2b115bbab50b8f70a103764c7e.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1381a-antes-deve-excluir-gestante-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="0.503">
<failure message="crud.e2e.test.ts:116:3 deve buscar gestantes" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:116:3 › Testes E2E - CRUD de Gestantes › deve buscar gestantes ───────

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-27\d75363f6503d7508e73782682d145ae2.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-ee4f7-antes-deve-buscar-gestantes-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="0.496">
<failure message="crud.e2e.test.ts:142:3 deve validar campos obrigatórios" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:142:3 › Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-28\fe100f9683af4e7da584780d8e4fe64a.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-0e494-validar-campos-obrigatórios-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="0.521">
<failure message="crud.e2e.test.ts:160:3 deve cancelar criação de gestante" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:160:3 › Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-29\1fac47871ae2a54b1752b9b140cad92a.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-d9af0-ancelar-criação-de-gestante-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="0.6">
<failure message="crud.e2e.test.ts:180:3 deve exibir mensagens de erro para dados inválidos" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:180:3 › Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-30\55951b5fd47e9481706e7c5a4feccb6a.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-f1835-e-erro-para-dados-inválidos-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="0.524">
<failure message="crud.e2e.test.ts:203:3 deve funcionar em dispositivos móveis" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:203:3 › Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-31\aa75d45cc5fc363608fc1ed9075b0abd.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-60072-onar-em-dispositivos-móveis-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="0.502">
<failure message="crud.e2e.test.ts:225:3 deve manter estado durante navegação" type="FAILURE">
<![CDATA[  [webkit] › crud.e2e.test.ts:225:3 › Testes E2E - CRUD de Gestantes › deve manter estado durante navegação 

    Error: browserContext.newPage: Protocol error (Screencast.startVideo): Failed to open file 'C:\Users\<USER>\Downloads\gestão-materna-integrada\test-results\.playwright-artifacts-32\cce51c186aebb183f25e13f448e7077c.webm' for writing: No such file or directory

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-7cdd9-er-estado-durante-navegação-webkit\test-failed-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="Mobile Chrome" tests="11" failures="4" skipped="7" time="44.779" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="13.149">
<failure message="crud.e2e.test.ts:15:3 deve navegar para página de gestantes" type="FAILURE">
<![CDATA[  [Mobile Chrome] › crud.e2e.test.ts:15:3 › Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes 

    Error: page.click: Target page, context or browser has been closed
    Call log:
      - waiting for locator('[data-testid="menu-gestantes"]')


      15 |   test('deve navegar para página de gestantes', async ({ page }) => {
      16 |     // Clicar no menu de gestantes
    > 17 |     await page.click('[data-testid="menu-gestantes"]');
         |                ^
      18 |     
      19 |     // Verificar se chegou na página correta
      20 |     await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:17:16

    Error: browserContext._wrapApiCall: Target page, context or browser has been closed

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-1d893-ar-para-página-de-gestantes-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="12.361">
<failure message="crud.e2e.test.ts:24:3 deve listar gestantes existentes" type="FAILURE">
<![CDATA[  [Mobile Chrome] › crud.e2e.test.ts:24:3 › Testes E2E - CRUD de Gestantes › deve listar gestantes existentes 

    Error: page.waitForSelector: Target page, context or browser has been closed
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      26 |     
      27 |     // Aguardar a tabela carregar
    > 28 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      29 |     
      30 |     // Verificar se há gestantes na lista
      31 |     const rows = page.locator('[data-testid="pregnant-table"] tbody tr');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:28:16

    Error: browserContext._wrapApiCall: Target page, context or browser has been closed

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-80fa6-listar-gestantes-existentes-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="11.626">
<failure message="crud.e2e.test.ts:35:3 deve criar nova gestante" type="FAILURE">
<![CDATA[  [Mobile Chrome] › crud.e2e.test.ts:35:3 › Testes E2E - CRUD de Gestantes › deve criar nova gestante 

    Error: page.click: Target page, context or browser has been closed
    Call log:
      - waiting for locator('[data-testid="new-pregnant-button"]')


      37 |     
      38 |     // Clicar no botão "Nova Gestante"
    > 39 |     await page.click('[data-testid="new-pregnant-button"]');
         |                ^
      40 |     
      41 |     // Aguardar modal abrir
      42 |     await page.waitForSelector('[data-testid="pregnant-form-modal"]');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:39:16

    Error: browserContext._wrapApiCall: Target page, context or browser has been closed

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-df2cb-es-deve-criar-nova-gestante-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="7.643">
<failure message="crud.e2e.test.ts:59:3 deve editar gestante existente" type="FAILURE">
<![CDATA[  [Mobile Chrome] › crud.e2e.test.ts:59:3 › Testes E2E - CRUD de Gestantes › deve editar gestante existente 

    Error: page.waitForSelector: Target page, context or browser has been closed
    Call log:
      - waiting for locator('[data-testid="pregnant-table"]') to be visible


      61 |     
      62 |     // Aguardar tabela carregar
    > 63 |     await page.waitForSelector('[data-testid="pregnant-table"]');
         |                ^
      64 |     
      65 |     // Clicar no primeiro botão de editar
      66 |     await page.click('[data-testid="pregnant-table"] button:has-text("Editar")');
        at C:\Users\<USER>\Downloads\gestão-materna-integrada\src\test\e2e\crud.e2e.test.ts:63:16

    Error: browserContext._wrapApiCall: Target page, context or browser has been closed

    Error Context: ..\..\..\test-results\crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|crud.e2e-Testes-E2E---CRUD-59ad5-e-editar-gestante-existente-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="Microsoft Edge" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="crud.e2e.test.ts" timestamp="2025-05-28T22:16:54.850Z" hostname="Google Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Testes E2E - CRUD de Gestantes › deve navegar para página de gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve listar gestantes existentes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve criar nova gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve editar gestante existente" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve excluir gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve buscar gestantes" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve validar campos obrigatórios" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve cancelar criação de gestante" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve exibir mensagens de erro para dados inválidos" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve funcionar em dispositivos móveis" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Testes E2E - CRUD de Gestantes › deve manter estado durante navegação" classname="crud.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>