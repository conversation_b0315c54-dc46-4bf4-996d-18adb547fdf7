"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const genai_1 = require("@google/genai");
// Carregar variáveis de ambiente
dotenv_1.default.config();
async function testGeminiAPI() {
    console.log('🧪 Testando nova API do Gemini...');
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
        console.error('❌ GEMINI_API_KEY não encontrada no .env');
        process.exit(1);
    }
    console.log('🔑 API Key encontrada:', apiKey.substring(0, 10) + '...');
    try {
        // Inicializar cliente
        const ai = new genai_1.GoogleGenAI({ apiKey });
        console.log('✅ Cliente Gemini inicializado');
        // Teste simples
        console.log('📤 Enviando prompt de teste...');
        const response = await ai.models.generateContent({
            model: "gemini-2.0-flash",
            contents: "Analise o sentimento desta mensagem de uma gestante: 'Estou muito feliz com minha gravidez, mas às vezes sinto um pouco de ansiedade sobre o parto.' Responda apenas: positivo, negativo, neutro ou urgente.",
        });
        console.log('📥 Resposta recebida:', response.text || 'Resposta vazia');
        // Teste de análise de sentimento
        console.log('\n🔍 Testando análise de sentimento estruturada...');
        const sentimentPrompt = `
Analise o sentimento e categorize esta mensagem de uma gestante:
"Estou com muita dor abdominal e sangramento. Preciso de ajuda urgente!"

Responda APENAS no formato JSON:
{
  "sentiment": {
    "type": "urgent",
    "score": 0.9,
    "confidence": 0.8
  },
  "category": "emergency",
  "priority": "urgent",
  "keywords": ["dor", "sangramento", "urgente"],
  "needs": ["atendimento médico imediato"],
  "suggestions": ["Entrar em contato imediatamente", "Verificar sinais vitais"]
}`;
        const sentimentResponse = await ai.models.generateContent({
            model: "gemini-2.0-flash",
            contents: sentimentPrompt,
        });
        console.log('📊 Análise de sentimento:', sentimentResponse.text || 'Resposta vazia');
        // Tentar fazer parse do JSON
        try {
            const responseText = sentimentResponse.text;
            if (responseText) {
                const parsed = JSON.parse(responseText);
                console.log('✅ JSON válido recebido:', parsed);
            }
            else {
                console.log('⚠️  Resposta vazia recebida');
            }
        }
        catch (parseError) {
            console.log('⚠️  Resposta não é JSON válido, mas API funcionou');
        }
        console.log('\n🎉 Teste concluído com sucesso!');
        console.log('✅ Nova API do Gemini está funcionando corretamente');
    }
    catch (error) {
        console.error('❌ Erro no teste:', error);
        if (error instanceof Error) {
            console.error('Mensagem:', error.message);
            console.error('Stack:', error.stack);
        }
        process.exit(1);
    }
}
testGeminiAPI();
