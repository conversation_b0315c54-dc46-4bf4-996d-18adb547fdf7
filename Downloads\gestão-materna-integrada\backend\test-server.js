const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');

const app = express();
const PORT = process.env.PORT || 3000;

// Middlewares básicos
app.use(cors());
app.use(express.json());

// Conectar ao MongoDB (opcional para teste)
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/gestao-materna-test');
    console.log('✅ MongoDB conectado com sucesso');
  } catch (error) {
    console.log('⚠️  MongoDB não conectado (opcional para teste):', error.message);
  }
};

// Rotas de teste
app.get('/', (req, res) => {
  res.json({
    message: '🚀 Backend Gestão Materna Integrada',
    status: 'running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      auth: '/api/auth/*',
      contacts: '/api/contacts/*'
    }
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

// Rota de teste para autenticação
app.post('/api/auth/test', (req, res) => {
  res.json({
    message: 'Rota de autenticação funcionando',
    received: req.body
  });
});

// Rota de teste para contatos
app.get('/api/contacts/test', (req, res) => {
  res.json({
    message: 'Rota de contatos funcionando',
    sampleContacts: [
      {
        id: 1,
        name: 'Ana Silva',
        phone: '+5511999999999',
        pregnancyStage: 'second_trimester'
      },
      {
        id: 2,
        name: 'Maria Santos',
        phone: '+5511888888888',
        pregnancyStage: 'third_trimester'
      }
    ]
  });
});

// Middleware de erro
app.use((err, req, res, next) => {
  console.error('Erro:', err);
  res.status(500).json({
    error: 'Erro interno do servidor',
    message: err.message
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    path: req.originalUrl,
    method: req.method
  });
});

// Iniciar servidor
const startServer = async () => {
  await connectDB();
  
  app.listen(PORT, () => {
    console.log('🚀 Servidor iniciado com sucesso!');
    console.log(`📡 Rodando em: http://localhost:${PORT}`);
    console.log(`🌍 Ambiente: ${process.env.NODE_ENV || 'development'}`);
    console.log('📋 Rotas disponíveis:');
    console.log('   GET  / - Informações do servidor');
    console.log('   GET  /health - Status de saúde');
    console.log('   POST /api/auth/test - Teste de autenticação');
    console.log('   GET  /api/contacts/test - Teste de contatos');
    console.log('');
    console.log('✅ Backend pronto para receber requisições!');
  });
};

startServer().catch(console.error);
