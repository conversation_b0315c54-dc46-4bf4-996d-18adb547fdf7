/*!
 * Copyright 2021 WPPConnect Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Download the blob of a media message
 *
 * ```javascript
 * // Get a blob file
 * await WPP.chat.downloadMedia('true_[number]@c.us_ABCDEF');
 *
 * // Get a base64Content
 * await WPP.chat.downloadMedia('true_[number]@c.us_ABCDEF').then(WPP.util.blobToBase64);
 * ```
 *
 * @category Message
 */
export declare function downloadMedia(id: string): Promise<Blob>;
