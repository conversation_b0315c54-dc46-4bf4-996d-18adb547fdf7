{"name": "@tootallnate/quickjs-emscripten", "version": "0.23.0", "main": "dist/index.js", "sideEffects": false, "license": "MIT", "keywords": ["eval", "quickjs", "vm", "interpreter", "runtime", "safe", "emscripten", "wasm"], "repository": {"type": "git", "url": "https://github.com/justjake/quickjs-emscripten"}, "files": ["c/interface.c", "dist/**/*", "!dist/*.test.js", "!dist/*.tsbuildinfo"], "scripts": {"tarball": "make build/quickjs-emscripten.tgz", "clean": "make clean", "tsc": "node_modules/.bin/tsc", "build": "make dist", "doc": "typedoc", "test": "TS_NODE_TRANSPILE_ONLY=true mocha 'ts/**/*.test.ts'", "test-dist": "cd dist && TS_NODE_TRANSPILE_ONLY=true mocha --require source-map-support/register *.test.js", "test-fast": "TEST_NO_ASYNC=true yarn test 'ts/**/*.test.ts'", "test-all": "TEST_LEAK=1 yarn test && TEST_LEAK=1 yarn test-dist", "prettier": "prettier --write .", "prettier-check": "prettier --check .", "update-quickjs": "git subtree pull --prefix=quickjs --squash **************:bellard/quickjs.git master", "smoketest-node": "yarn tarball && ./scripts/smoketest-node.sh", "smoketest-cra": "yarn tarball && ./scripts/smoketest-website.sh"}, "devDependencies": {"@types/emscripten": "^1.38.0", "@types/fs-extra": "^9.0.13", "@types/mocha": "^5.2.7", "@types/node": "^13.1.4", "fs-extra": "^10.0.1", "markserv": "^1.17.4", "mocha": "7.2.0", "node-fetch-commonjs": "^3.1.1", "prettier": "2.8.4", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typedoc": "^0.22.0", "typedoc-plugin-inline-sources": "^1.0.1", "typedoc-plugin-markdown": "^3.11.12", "typescript": "^4.9.5"}}